Running 1 model(s)...

--- Running Model 1/1: saits ---
🚀 [MAXIMUM PERFORMANCE] Starting optimized training for saits
   Expected speedup: 4-6x
   Optimization level: aggressive
   GPU strategy: modern_gpu
[MEM] Applying memory optimization with Maximum Performance
[MEM] Memory cleared
🎯 GPU Hardware: NVIDIA T550 Laptop GPU (Compute 7.5)
   ⚡ Mixed precision enabled for modern GPU
🚀 Attempting optimized pipeline (level: aggressive)
🚀 Starting Optimized Phase 1 Training (Level: aggressive)...
   Model: SAITS (Self-Attention)
   Target: P-WAVE
   Features: ['GR', 'NPHI', 'RHOB', 'RT', 'TVDSS']
   📊 Dataset size: 57,856 rows, 8 wells (avg: 7232 rows/well)
   🔍 Pre-checking data sufficiency for aggressive optimization...
   📊 Wells analysis: 8/8 wells likely to pass (threshold: 11)
\n📊 Step 1: Optimized Data Preparation...
   Performing quick data quality assessment...
   📊 Small dataset detected (6,000 elements) - using adaptive quality threshold
   📈 Data quality score: 0.657 (finite rate: 66.1%)
   📊 Standard preprocessing required (score: 0.657 < 0.800)
Using enhanced normalization with winsorization...
Normalized 'GR': method=standard, valid_data=51983/57856 (89.8%)
Normalized 'NPHI': method=standard, valid_data=42700/57856 (73.8%)
Normalized 'RHOB': method=standard, valid_data=36835/57856 (63.7%)
Normalized 'RT': method=standard, valid_data=47696/57856 (82.4%)
Normalized 'TVDSS': method=standard, valid_data=55307/57856 (95.6%)
Normalized 'P-WAVE': method=standard, valid_data=37801/57856 (65.3%)
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: TRAINING
Creating sequences | Current: EB-1: 100%|█████████████████████| 8/8 wells [00:10<00:00,  1.26s/well]

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 23
   • Total sequences created: 33,543
Enhanced sequences shape: (33543, 64, 6), type: <class 'numpy.ndarray'>, dtype: float64
   Created 33543 sequences in 10.31s
\n🔍 Step 2: Vectorized Preprocessing Pipeline...
   🚀 Vectorized preprocessing: (33543, 64, 6)
   Vectorized preprocessing completed in 0.68s
\n🔧 Step 3: Smart Validation...
   Validation completed in 0.01s
   Sequences valid: ✅ STABLE
\n🎯 Step 4: Direct Tensor Training...
   📚 IMPUTATION MODE: Creating training sequences with missing values
Using enhanced missing value introduction with realistic patterns...
Introduced 26.0% missing values (3354999 elements)
Pattern: 1159245 random + 2195754 chunked
Enhanced missing sequences shape: (33543, 64, 6), type: <class 'numpy.ndarray'>, dtype: float64
   Tensor preparation completed in 4.04s
   Training tensor shape: torch.Size([33543, 64, 6])
   Truth tensor shape: torch.Size([33543, 64, 6])
   Efficient synthetic DataFrame created: (100, 8)
--- Running Deep Learning Model: SAITS (Self-Attention) ---
CuDNN benchmark enabled for performance.
Automatic well assignment:
  - Training/Validation wells: ['WELL_8', 'WELL_1', 'WELL_5', 'WELL_0', 'WELL_7', 'WELL_2', 'WELL_9', 'WELL_4']
  - Test wells: ['WELL_3', 'WELL_6']
   Using adaptive validation ratio: 15.0% (median well size: 10)
   🚨 Very small wells detected (min: 10) - reduced validation ratio to 10.0%
   Minimum well size threshold: 6 (validation ratio: 10.0%)
Flexible Split Report:
  - Wells for Training/Validation: 8
  - Wells for Final Testing: 2
  - Train Samples (Shallow part of train wells): 72
  - Validation Samples (Deeper part of train wells): 8
  - Test Samples (Entirely separate wells): 20

Running data leakage detection on splits...
🔍 Performing comprehensive data leakage check...
============================================================
🔍 Detecting perfect correlation leakage...
🚨 POTENTIAL DATA LEAKAGE DETECTED!
   TRAIN split:
     - NPHI: correlation = 0.959
     - RHOB: correlation = 0.994
     - RT: correlation = 1.000
     - TVDSS: correlation = 1.000
🕐 Validating temporal split integrity...
✅ No temporal leakage detected in splits
🎯 Detecting target leakage in features...
🚨 TARGET LEAKAGE DETECTED!
   Suspicious correlations:
     - RHOB: 0.9923
     - RT: 0.9996
     - TVDSS: 0.9999
============================================================
📊 COMPREHENSIVE LEAKAGE CHECK SUMMARY
============================================================
🚨 DATA LEAKAGE DETECTED!
   Failed checks: 2/3
   Data quality score: 0.33

📋 All Recommendations:
   ⚠️ Suspiciously high correlations detected (>0.95)
   • Check if target information is leaking into features
   • Verify temporal ordering of data splits
   • Review feature engineering process
   • Consider removing highly correlated features
   ✅ No temporal leakage detected in splits
   🚨 Target leakage detected in features!
   • Remove features that are identical to target
   • Check feature engineering pipeline for target contamination
   • Verify that future target values are not used as features
   • Review data preprocessing steps
============================================================
⚠️ Data leakage detected but continuing with training...
   Review the recommendations above to improve data quality.

Using enhanced normalization with winsorization...
Normalized 'GR': method=standard, valid_data=72/72 (100.0%)
Normalized 'NPHI': method=standard, valid_data=72/72 (100.0%)
Normalized 'RHOB': method=standard, valid_data=72/72 (100.0%)
Normalized 'RT': method=standard, valid_data=72/72 (100.0%)
Normalized 'TVDSS': method=standard, valid_data=72/72 (100.0%)
Normalized 'P-WAVE': method=standard, valid_data=72/72 (100.0%)
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: TRAINING
Creating sequences | Current: WELL_4: 100%|██████████████████| 8/8 wells [00:00<00:00, 998.91well/s]

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 8
   • Total sequences created: 0
Enhanced sequences shape: (0, 64, 6), type: <class 'numpy.ndarray'>, dtype: float32
⚠️ Enhanced sequence creation failed - falling back to standard method
   Enhanced result shape: (0, 64, 6) (expected: (n_sequences, 64, 6))
   Data quality check:
     - Total wells: 8
     - Total rows: 72
     - Feature columns: ['GR', 'NPHI', 'RHOB', 'RT', 'TVDSS', 'P-WAVE']
     - Sequence length: 64, step: 1
   🔄 Trying enhanced preprocessing with smaller sequence length: 32
Sequence creation mode: TRAINING
Creating sequences | Current: WELL_4: 100%|█████████████████| 8/8 wells [00:00<00:00, 1947.33well/s]

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 8
   • Total sequences created: 0
   🔄 Trying enhanced preprocessing with smaller sequence length: 16
Sequence creation mode: TRAINING
Creating sequences | Current: WELL_4: 100%|█████████████████| 8/8 wells [00:00<00:00, 3638.52well/s]

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 8
   • Total sequences created: 0
   🔄 Trying enhanced preprocessing with smaller sequence length: 8
Sequence creation mode: TRAINING
Creating sequences | Current: WELL_4: 100%|██████████████████| 8/8 wells [00:00<00:00, 505.19well/s]

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 8
   • Total sequences created: 16
   ✅ Enhanced preprocessing succeeded with sequence length 8
Using enhanced normalization with winsorization...
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: TRAINING
Creating sequences | Current: WELL_4: 100%|██████████████████| 8/8 wells [00:00<00:00, 993.20well/s]

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 8
   • Total sequences created: 0
Enhanced sequences shape: (0, 64, 6), type: <class 'numpy.ndarray'>, dtype: float32
⚠️ Enhanced sequence creation failed - falling back to standard method
   Enhanced result shape: (0, 64, 6) (expected: (n_sequences, 64, 6))
   Data quality check:
     - Total wells: 8
     - Total rows: 8
     - Feature columns: ['GR', 'NPHI', 'RHOB', 'RT', 'TVDSS', 'P-WAVE']
     - Sequence length: 64, step: 1
   🔄 Trying enhanced preprocessing with smaller sequence length: 32
Sequence creation mode: TRAINING
Creating sequences | Current: WELL_4: 100%|█████████████████| 8/8 wells [00:00<00:00, 1038.71well/s]

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 8
   • Total sequences created: 0
   🔄 Trying enhanced preprocessing with smaller sequence length: 16
Sequence creation mode: TRAINING
Creating sequences | Current: WELL_4: 100%|█████████████████| 8/8 wells [00:00<00:00, 2228.35well/s]

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 8
   • Total sequences created: 0
   🔄 Trying enhanced preprocessing with smaller sequence length: 8
Sequence creation mode: TRAINING
Creating sequences | Current: WELL_4: 100%|█████████████████| 8/8 wells [00:00<00:00, 1282.86well/s]

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 8
   • Total sequences created: 0
   ⚠️ Enhanced preprocessing failed with all sequence lengths - falling back to standard method
Creating basic sequences | Current: WELL_9: 100%|████████████| 8/8 wells [00:00<00:00, 512.27well/s]

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 8
   • Total sequences created: 0
Warning: No valid sequences could be created. Check data quality and sequence length.
📚 Introducing missingness for imputation training.
Using enhanced missing value introduction with realistic patterns...
Introduced 15.5% missing values (119 elements)
Pattern: 69 random + 50 chunked
Enhanced missing sequences shape: (16, 8, 6), type: <class 'numpy.ndarray'>, dtype: float64
Using enhanced missing value introduction with realistic patterns...
❌ Optimized training failed: Input sequences must be 3D array (n_sequences, seq_len, n_features)
   Falling back to original function...
--- Running Deep Learning Model: SAITS (Self-Attention) ---
CuDNN benchmark enabled for performance.
Automatic well assignment:
  - Training/Validation wells: ['B-G-10', 'B-L-9', 'B-G-6', 'EB-1', 'B-L-1', 'B-L-6']
  - Test wells: ['B-L-2.G1', 'B-L-15']
   Using adaptive validation ratio: 30.0% (median well size: 7979)
   Minimum well size threshold: 12 (validation ratio: 30.0%)
Flexible Split Report:
  - Wells for Training/Validation: 6
  - Wells for Final Testing: 2
  - Train Samples (Shallow part of train wells): 32126
  - Validation Samples (Deeper part of train wells): 13772
  - Test Samples (Entirely separate wells): 11958

Running data leakage detection on splits...
🔍 Performing comprehensive data leakage check...
============================================================
🔍 Detecting perfect correlation leakage...
🚨 POTENTIAL DATA LEAKAGE DETECTED!
   TEST split:
     - TVDSS: correlation = 0.966
🕐 Validating temporal split integrity...
✅ No temporal leakage detected in splits
🎯 Detecting target leakage in features...
✅ No target leakage detected in features
============================================================
📊 COMPREHENSIVE LEAKAGE CHECK SUMMARY
============================================================
🚨 DATA LEAKAGE DETECTED!
   Failed checks: 1/3
   Data quality score: 0.67

📋 All Recommendations:
   ⚠️ Suspiciously high correlations detected (>0.95)
   • Check if target information is leaking into features
   • Verify temporal ordering of data splits
   • Review feature engineering process
   • Consider removing highly correlated features
   ✅ No temporal leakage detected in splits
   ✅ No target leakage detected in features
============================================================
⚠️ Data leakage detected but continuing with training...
   Review the recommendations above to improve data quality.

Using enhanced normalization with winsorization...
Normalized 'GR': method=standard, valid_data=31964/32126 (99.5%)
Normalized 'NPHI': method=standard, valid_data=24749/32126 (77.0%)
Normalized 'RHOB': method=standard, valid_data=20743/32126 (64.6%)
Normalized 'RT': method=standard, valid_data=31442/32126 (97.9%)
Normalized 'TVDSS': method=standard, valid_data=32126/32126 (100.0%)
Normalized 'P-WAVE': method=standard, valid_data=21526/32126 (67.0%)
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: TRAINING
Creating sequences | Current: B-L-6: 100%|████████████████████| 6/6 wells [00:05<00:00,  1.12well/s]

📊 Well Processing Summary:
   • Total wells processed: 6
   • Successful: 6
   • Failed: 0
   • Total valid intervals: 15
   • Total sequences created: 18,052
Enhanced sequences shape: (18052, 64, 6), type: <class 'numpy.ndarray'>, dtype: float64
Using enhanced normalization with winsorization...
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: TRAINING
Creating sequences | Current: B-L-6: 100%|████████████████████| 6/6 wells [00:02<00:00,  2.85well/s]

📊 Well Processing Summary:
   • Total wells processed: 6
   • Successful: 6
   • Failed: 0
   • Total valid intervals: 6
   • Total sequences created: 5,931
Enhanced sequences shape: (5931, 64, 6), type: <class 'numpy.ndarray'>, dtype: float64
📚 Introducing missingness for imputation training.
Using enhanced missing value introduction with realistic patterns...
Introduced 26.1% missing values (1806834 elements)
Pattern: 623877 random + 1182957 chunked
Enhanced missing sequences shape: (18052, 64, 6), type: <class 'numpy.ndarray'>, dtype: float64
Using enhanced missing value introduction with realistic patterns...
Introduced 26.0% missing values (592906 elements)
Pattern: 204975 random + 387931 chunked
Enhanced missing sequences shape: (5931, 64, 6), type: <class 'numpy.ndarray'>, dtype: float64
Converting sequences to tensors...
   Processing train_sequences_missing: current type is <class 'numpy.ndarray'>
   Changing train_sequences_missing dtype from float64 to float32.
   train_sequences_missing tensor created with shape: torch.Size([18052, 64, 6]), dtype: torch.float32
   Processing train_sequences_true: current type is <class 'numpy.ndarray'>
   Changing train_sequences_true dtype from float64 to float32.
   train_sequences_true tensor created with shape: torch.Size([18052, 64, 6]), dtype: torch.float32
   Processing val_sequences_missing: current type is <class 'numpy.ndarray'>
   Changing val_sequences_missing dtype from float64 to float32.
   val_sequences_missing tensor created with shape: torch.Size([5931, 64, 6]), dtype: torch.float32
   Processing val_sequences_true: current type is <class 'numpy.ndarray'>
   Changing val_sequences_true dtype from float64 to float32.
   val_sequences_true tensor created with shape: torch.Size([5931, 64, 6]), dtype: torch.float32
   Tensor shapes - Train: torch.Size([18052, 64, 6]), Truth: torch.Size([18052, 64, 6])
🔧 Applied fixed parameters for SAITS (Self-Attention): {}
🔍 Pre-checking PyPOTS availability for SAITS (Self-Attention) model...
🔄 TensorFlow cache reset - will re-check availability
🔧 TensorFlow environment configured for compatibility (TF not available: No module named 'tensorflow')
❌ PyPOTS availability check failed for SAITS (Self-Attention): PyPOTS is required for SAITS (Self-Attention) model but is not available: PyPOTS requires TensorFlow: No module named 'tensorflow'
❌ Failed to create SAITS (Self-Attention) model: PyPOTS is required for SAITS (Self-Attention) model. Please install with: pip install pypots
   Parameters: {'sequence_len': 64, 'n_features': 6, 'n_layers': 2, 'd_model': 256, 'n_heads': 4, 'epochs': 40, 'batch_size': 64, 'learning_rate': 0.002, 'early_stopping_patience': 12, 'dropout': 0.1, 'device': None, 'use_mixed_precision': True, 'gpu_config': {'mixed_precision_enabled': True, 'gpu_preprocessing_enabled': True, 'compute_capability': '7.5', 'optimization_strategy': 'modern_gpu'}}
❌ Model creation failed: PyPOTS is required for SAITS (Self-Attention) model. Please install with: pip install pypots
✅ Optimized pipeline completed successfully in 25.79s
[MEM] Memory cleared
✅ [MAXIMUM PERFORMANCE] Completed optimized training for saits with memory optimization
saits completed successfully

Batch execution summary:
   • Successful models: 1
   • Failed models: 0
   • Successful: saits
Multi-model execution completed

Step 10: Configure output options

Model Performance Summary:
----------------------------------------------------------------------
Model           MAE        R²         RMSE       Composite
----------------------------------------------------------------------
----------------------------------------------------------------------
No successful models to evaluate.