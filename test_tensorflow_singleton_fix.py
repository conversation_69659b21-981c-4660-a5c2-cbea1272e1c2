#!/usr/bin/env python3
"""
Test script to verify the TensorFlow PyExceptionRegistry singleton fix.

This script tests that we can perform multiple force rechecks and SAITS model
initializations without triggering the "PyExceptionRegistry::Init() already called" error.
"""

import sys
import os

def test_tensorflow_cache_reset():
    """Test that TensorFlow cache reset doesn't cause singleton errors."""
    print("🧪 Testing TensorFlow cache reset functionality...")
    
    try:
        # Import our compatibility module
        from utils import tensorflow_compatibility as tfc
        
        # Test 1: Initial import
        print("\n📋 Test 1: Initial TensorFlow import")
        success1, tf_module1, error1 = tfc.import_tensorflow_safe()
        if success1:
            print(f"✅ Initial import successful: TensorFlow {tf_module1.__version__}")
        else:
            print(f"❌ Initial import failed: {error1}")
            return False
        
        # Test 2: Force recheck (this previously caused the PyExceptionRegistry error)
        print("\n📋 Test 2: Force recheck (potential singleton conflict)")
        success2, tf_module2, error2 = tfc.import_tensorflow_safe(force_recheck=True)
        if success2:
            print(f"✅ Force recheck successful: TensorFlow {tf_module2.__version__}")
        else:
            print(f"❌ Force recheck failed: {error2}")
            return False
        
        # Test 3: Multiple force rechecks
        print("\n📋 Test 3: Multiple force rechecks")
        for i in range(3):
            success, tf_module, error = tfc.import_tensorflow_safe(force_recheck=True)
            if success:
                print(f"✅ Force recheck {i+1} successful")
            else:
                print(f"❌ Force recheck {i+1} failed: {error}")
                return False
        
        # Test 4: PyPOTS import with force recheck
        print("\n📋 Test 4: PyPOTS import with force recheck")
        success4, modules4, error4 = tfc.import_pypots_safe(force_recheck=True)
        if success4:
            print("✅ PyPOTS import with force recheck successful")
        else:
            print(f"❌ PyPOTS import failed: {error4}")
            # This might fail if PyPOTS isn't installed, which is OK for this test
        
        print("\n✅ All TensorFlow cache reset tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_saits_model_initialization():
    """Test that SAITS model can be initialized multiple times."""
    print("\n🧪 Testing SAITS model initialization...")
    
    try:
        # Test multiple SAITS model initializations
        from models.advanced_models.saits_model import SAITSModel
        
        print("\n📋 Test 1: First SAITS model initialization")
        model1 = SAITSModel(n_features=4, sequence_len=32, epochs=1)
        print("✅ First SAITS model created successfully")
        
        print("\n📋 Test 2: Second SAITS model initialization")
        model2 = SAITSModel(n_features=4, sequence_len=32, epochs=1)
        print("✅ Second SAITS model created successfully")
        
        print("\n✅ SAITS model initialization tests passed!")
        return True
        
    except ImportError as e:
        print(f"⚠️ SAITS model not available (expected if PyPOTS not installed): {e}")
        return True  # This is OK for the test
    except Exception as e:
        print(f"❌ SAITS test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🚀 TensorFlow Singleton Fix Verification")
    print("=" * 60)
    
    # Test 1: TensorFlow cache reset
    test1_passed = test_tensorflow_cache_reset()
    
    # Test 2: SAITS model initialization
    test2_passed = test_saits_model_initialization()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Summary:")
    print(f"   TensorFlow cache reset: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"   SAITS model initialization: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 All tests passed! The PyExceptionRegistry fix is working correctly.")
        return 0
    else:
        print("\n❌ Some tests failed. The fix may need additional work.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
