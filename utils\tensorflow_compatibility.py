"""
TensorFlow Compatibility Module

This module provides compatibility fixes and fallback mechanisms for TensorFlow
import issues, particularly on Windows systems with DLL loading problems.

Key Features:
- Graceful TensorFlow import handling
- CPU-only fallback for DLL issues
- PyPOTS compatibility layer
- Environment detection and fixes

Author: ML Log Prediction System
Date: 2025-08-19
"""

import warnings
import os
import sys
from typing import Optional, Dict, Any, Tuple

# Global flags for TensorFlow availability
TENSORFLOW_AVAILABLE = False
TENSORFLOW_GPU_AVAILABLE = False
TENSORFLOW_VERSION = None
_TENSORFLOW_CHECKED = False  # Flag to prevent repeated checks

def reset_tensorflow_cache():
    """
    Reset TensorFlow cache to force re-checking.

    Note: This function only resets our Python-level tracking variables.
    It does NOT delete TensorFlow modules from sys.modules to avoid
    TensorFlow singleton initialization conflicts (PyExceptionRegistry error).
    """
    global TENSORFLOW_AVAILABLE, TENSORFLOW_GPU_AVAILABLE, TENSORFLOW_VERSION, _TENSORFLOW_CHECKED
    TENSORFLOW_AVAILABLE = False
    TENSORFLOW_GPU_AVAILABLE = False
    TENSORFLOW_VERSION = None
    _TENSORFLOW_CHECKED = False
    print("🔄 TensorFlow cache reset - will re-check availability")

def setup_tensorflow_environment():
    """
    Set up TensorFlow environment variables to avoid common issues.

    Note: This function no longer performs aggressive module cleanup to avoid
    TensorFlow singleton initialization conflicts.
    """
    # Suppress TensorFlow warnings
    os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'

    # Try to import TensorFlow to check if it works (without module cleanup)
    try:
        import tensorflow as tf
        print(f"🔧 TensorFlow environment configured for compatibility (TF {tf.__version__} available)")
        return True
    except Exception as e:
        print(f"🔧 TensorFlow environment configured for compatibility (TF not available: {e})")
        return False


def import_tensorflow_safe(force_recheck: bool = False) -> Tuple[bool, Optional[Any], Optional[str]]:
    """
    Safely import TensorFlow with fallback mechanisms.

    Args:
        force_recheck: If True, bypass cache and re-check TensorFlow availability

    Returns:
        Tuple of (success, tensorflow_module, error_message)
    """
    global TENSORFLOW_AVAILABLE, TENSORFLOW_GPU_AVAILABLE, TENSORFLOW_VERSION, _TENSORFLOW_CHECKED

    # Reset cache if force_recheck is True
    if force_recheck:
        reset_tensorflow_cache()

    # Return cached result if already checked (unless force_recheck)
    if _TENSORFLOW_CHECKED and not force_recheck:
        if TENSORFLOW_AVAILABLE:
            # TensorFlow should already be imported, get it from sys.modules
            import sys
            if 'tensorflow' in sys.modules:
                return True, sys.modules['tensorflow'], None
            else:
                # Fallback to regular import
                import tensorflow as tf
                return True, tf, None
        else:
            return False, None, "TensorFlow not available (cached result)"

    # Set up environment first (only sets env vars, no module cleanup)
    setup_tensorflow_environment()

    try:
        # Try importing TensorFlow
        import tensorflow as tf
        TENSORFLOW_AVAILABLE = True
        TENSORFLOW_VERSION = tf.__version__
        _TENSORFLOW_CHECKED = True

        # Check GPU availability
        try:
            gpus = tf.config.list_physical_devices('GPU')
            TENSORFLOW_GPU_AVAILABLE = len(gpus) > 0
            if TENSORFLOW_GPU_AVAILABLE:
                print(f"✅ TensorFlow {TENSORFLOW_VERSION} loaded with GPU support")
            else:
                print(f"✅ TensorFlow {TENSORFLOW_VERSION} loaded (CPU-only)")
        except Exception:
            TENSORFLOW_GPU_AVAILABLE = False
            print(f"✅ TensorFlow {TENSORFLOW_VERSION} loaded (CPU-only)")

        return True, tf, None

    except ImportError as e:
        TENSORFLOW_AVAILABLE = False
        _TENSORFLOW_CHECKED = True
        error_msg = str(e)

        # Check for specific DLL loading issues (only show once)
        if "DLL load failed" in error_msg or "_pywrap_tensorflow_internal" in error_msg:
            print("⚠️ TensorFlow DLL loading failed - this is a common Windows issue")
            print("💡 Suggestions:")
            print("   1. Install Microsoft Visual C++ Redistributable")
            print("   2. Use tensorflow-cpu instead of tensorflow")
            print("   3. Update to compatible numpy version")
            print("   4. Run: python fix_tensorflow_dll.py")

        elif "numpy" in error_msg and "dtypes" in error_msg:
            print("⚠️ TensorFlow-NumPy compatibility issue detected")
            print("💡 Solution: Update numpy to compatible version")
            print("   Run: pip install 'numpy>=1.21.0,<1.25.0'")

        warnings.warn(f"TensorFlow initialization failed: {e}")
        return False, None, error_msg

    except Exception as e:
        TENSORFLOW_AVAILABLE = False
        _TENSORFLOW_CHECKED = True
        error_msg = f"Unexpected TensorFlow import error: {str(e)}"
        return False, None, error_msg


def import_pypots_safe(force_recheck: bool = False) -> Tuple[bool, Optional[Any], Optional[str]]:
    """
    Safely import PyPOTS with TensorFlow compatibility handling.

    Args:
        force_recheck: If True, bypass cache and re-check TensorFlow availability

    Returns:
        Tuple of (success, pypots_modules, error_message)
    """
    try:
        # First ensure TensorFlow is available
        tf_success, tf_module, tf_error = import_tensorflow_safe(force_recheck=force_recheck)

        if not tf_success:
            return False, None, f"PyPOTS requires TensorFlow: {tf_error}"

        # Try importing PyPOTS components
        import pypots as _pypots
        from pypots.imputation import SAITS, BRITS
        from pypots.optim import Adam

        pypots_modules = {
            'pypots': _pypots,
            'SAITS': SAITS,
            'BRITS': BRITS,
            'Adam': Adam
        }

        print("✅ PyPOTS imported successfully with TensorFlow backend")
        return True, pypots_modules, None

    except ImportError as e:
        error_msg = f"PyPOTS import failed: {str(e)}"
        return False, None, error_msg
    except Exception as e:
        error_msg = f"Unexpected PyPOTS import error: {str(e)}"
        return False, None, error_msg


def get_tensorflow_status() -> Dict[str, Any]:
    """
    Get current TensorFlow status and configuration.

    Returns:
        Dictionary with TensorFlow status information
    """
    return {
        'tensorflow_available': TENSORFLOW_AVAILABLE,
        'tensorflow_gpu_available': TENSORFLOW_GPU_AVAILABLE,
        'tensorflow_version': TENSORFLOW_VERSION,
        'environment_configured': True
    }


def create_tensorflow_fallback():
    """
    Create fallback implementations for when TensorFlow is not available.
    """
    class MockTensorFlow:
        """Mock TensorFlow for fallback scenarios."""
        __version__ = "mock-fallback"

        @staticmethod
        def config():
            return MockConfig()

    class MockConfig:
        """Mock TensorFlow config."""
        @staticmethod
        def list_physical_devices(device_type='GPU'):
            return []


def get_tensorflow_gpu_info():
    """Get detailed TensorFlow GPU information."""
    try:
        tf_success, tf_module, _ = import_tensorflow_safe()
        if not tf_success or tf_module is None:
            return False, 0, []
        gpus = tf_module.config.list_physical_devices('GPU')
        if gpus:
            gpu_details = []
            for i, gpu in enumerate(gpus):
                try:
                    details = tf_module.config.experimental.get_device_details(gpu)
                    gpu_name = details.get('device_name', gpu.name)
                    gpu_details.append(f"GPU {i}: {gpu_name}")
                except Exception:
                    gpu_details.append(f"GPU {i}: Available")
            return True, len(gpus), gpu_details
        return False, 0, []
    except Exception as e:
        return False, 0, [f"Error: {e}"]


    return MockTensorFlow()


def configure_tensorflow_gpu_memory(tf_module=None, enable_memory_growth=True):
    """
    Configure TensorFlow GPU memory settings for optimal SAITS performance.

    Args:
        tf_module: TensorFlow module (if None, will import)
        enable_memory_growth: Whether to enable GPU memory growth

    Returns:
        bool: True if GPU configuration was successful
    """
    try:
        if tf_module is None:
            tf_success, tf_module, _ = import_tensorflow_safe()
            if not tf_success:
                return False

        gpus = tf_module.config.list_physical_devices('GPU')
        if not gpus:
            print("💻 No GPUs detected - using CPU mode")
            return False

        configured_gpus = 0
        for gpu in gpus:
            try:
                if enable_memory_growth:
                    tf_module.config.experimental.set_memory_growth(gpu, True)
                    print(f"🚀 GPU memory growth enabled for {gpu.name}")

                # Set memory limit if needed (optional)
                # tf_module.config.experimental.set_memory_limit(gpu, 4096)  # 4GB limit

                configured_gpus += 1
            except Exception as gpu_error:
                print(f"⚠️ Failed to configure GPU {gpu.name}: {gpu_error}")

        if configured_gpus > 0:
            print(f"✅ Successfully configured {configured_gpus} GPU(s) for TensorFlow")
            return True
        else:
            print("❌ Failed to configure any GPUs")
            return False

    except Exception as e:
        print(f"❌ GPU configuration failed: {e}")
        return False


def get_gpu_optimization_recommendations():
    """
    Provide GPU optimization recommendations for SAITS model.

    Returns:
        dict: GPU optimization recommendations
    """
    recommendations = {
        'gpu_available': TENSORFLOW_GPU_AVAILABLE,
        'tensorflow_version': TENSORFLOW_VERSION,
        'recommendations': []
    }

    if TENSORFLOW_GPU_AVAILABLE:
        recommendations['recommendations'].extend([
            "🚀 GPU detected - SAITS will benefit from GPU acceleration",
            "💡 Enable mixed precision training for better performance",
            "💡 Use larger batch sizes to maximize GPU utilization",
            "💡 Monitor GPU memory usage during training"
        ])
    else:
        recommendations['recommendations'].extend([
            "💻 CPU-only mode detected",
            "💡 Consider installing CUDA-compatible TensorFlow for GPU acceleration",
            "💡 Use smaller batch sizes to avoid memory issues",
            "💡 Consider using distributed training for large datasets"
        ])

    return recommendations


def validate_pypots_environment():
    """
    Comprehensive validation of PyPOTS environment for SAITS.

    Returns:
        dict: Validation results with detailed status
    """
    validation_results = {
        'tensorflow_status': 'unknown',
        'pypots_status': 'unknown',
        'gpu_status': 'unknown',
        'recommendations': [],
        'errors': []
    }

    try:
        # Check TensorFlow
        tf_success, tf_module, tf_error = import_tensorflow_safe()
        if tf_success:
            validation_results['tensorflow_status'] = 'available'
            validation_results['tensorflow_version'] = TENSORFLOW_VERSION

            # Check GPU
            if TENSORFLOW_GPU_AVAILABLE:
                validation_results['gpu_status'] = 'available'
                validation_results['recommendations'].append("🚀 GPU acceleration available for SAITS")
            else:
                validation_results['gpu_status'] = 'not_available'
                validation_results['recommendations'].append("💻 CPU-only mode - consider GPU setup")
        else:
            validation_results['tensorflow_status'] = 'failed'
            validation_results['errors'].append(f"TensorFlow: {tf_error}")

        # Check PyPOTS
        pypots_success, pypots_modules, pypots_error = import_pypots_safe()
        if pypots_success:
            validation_results['pypots_status'] = 'available'
            validation_results['recommendations'].append("✅ PyPOTS ready for SAITS model")
        else:
            validation_results['pypots_status'] = 'failed'
            validation_results['errors'].append(f"PyPOTS: {pypots_error}")

    except Exception as e:
        validation_results['errors'].append(f"Validation error: {str(e)}")

    return validation_results


def install_tensorflow_fix():
    """
    Provide installation instructions for fixing TensorFlow issues.
    """
    print("\n" + "="*60)
    print("🔧 TENSORFLOW INSTALLATION FIX")
    print("="*60)
    print("\nTo fix TensorFlow DLL loading issues on Windows:")
    print("\n1. Uninstall current TensorFlow:")
    print("   pip uninstall tensorflow tensorflow-gpu")
    print("\n2. Install unified TensorFlow with GPU support:")
    print("   pip install tensorflow>=2.10.0,<2.16.0")
    print("\n3. Fix NumPy compatibility:")
    print("   pip install 'numpy>=1.21.0,<1.25.0'")
    print("\n4. Install Visual C++ Redistributable:")
    print("   Download from Microsoft's official website")
    print("\n5. For GPU support, install CUDA and cuDNN:")
    print("   Follow TensorFlow GPU installation guide")
    print("\n6. Restart your Python environment")
    print("\n" + "="*60)


# Initialize TensorFlow on module import (with suppressed warnings for repeated imports)
if __name__ != "__main__":
    # Only run initialization if not being executed directly and not already checked
    if not _TENSORFLOW_CHECKED:
        tf_success, tf_module, tf_error = import_tensorflow_safe()
        if not tf_success and tf_error:
            # Only warn once, not on repeated imports
            warnings.warn(f"TensorFlow initialization failed: {tf_error}")
