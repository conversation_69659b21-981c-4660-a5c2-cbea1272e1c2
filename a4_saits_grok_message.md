📚 Introducing missingness for imputation training.
Using enhanced missing value introduction with realistic patterns...
Introduced 20.0% missing values (22595 elements)
Pattern: 10186 random + 12409 chunked
Enhanced missing sequences shape: (1179, 16, 6), type: <class 'numpy.ndarray'>, dtype: float64
Using enhanced missing value introduction with realistic patterns...
Introduced 19.9% missing values (7460 elements)
Pattern: 3378 random + 4082 chunked
Enhanced missing sequences shape: (391, 16, 6), type: <class 'numpy.ndarray'>, dtype: float64
Converting sequences to tensors...
   Processing train_sequences_missing: current type is <class 'numpy.ndarray'>
   Changing train_sequences_missing dtype from float64 to float32.
   train_sequences_missing tensor created with shape: torch.Size([1179, 16, 6]), dtype: torch.float32   
   Processing train_sequences_true: current type is <class 'numpy.ndarray'>
   Changing train_sequences_true dtype from float64 to float32.
   train_sequences_true tensor created with shape: torch.Size([1179, 16, 6]), dtype: torch.float32      
   Processing val_sequences_missing: current type is <class 'numpy.ndarray'>
   Changing val_sequences_missing dtype from float64 to float32.
   val_sequences_missing tensor created with shape: torch.Size([391, 16, 6]), dtype: torch.float32      
   Processing val_sequences_true: current type is <class 'numpy.ndarray'>
   Changing val_sequences_true dtype from float64 to float32.
   val_sequences_true tensor created with shape: torch.Size([391, 16, 6]), dtype: torch.float32
   Tensor shapes - Train: torch.Size([1179, 16, 6]), Truth: torch.Size([1179, 16, 6])
❌ Model creation failed: name 'train_sequences' is not defined
✅ Optimized pipeline completed successfully in 1.49s
[MEM] Memory cleared
✅ [MAXIMUM PERFORMANCE] Completed optimized training for saits with memory optimization
saits completed successfully

Batch execution summary:
   • Successful models: 1
   • Failed models: 0
   • Successful: saits
Multi-model execution completed

Step 10: Configure output options

Model Performance Summary:
----------------------------------------------------------------------
Model           MAE        R²         RMSE       Composite
----------------------------------------------------------------------
----------------------------------------------------------------------
No successful models to evaluate.

[Chart] Step 10: Select visualization and output options

Single model available: saits
1. Save results to files (with standard plots)
2. Enhanced visualization analysis (no file output)
3. Detailed single model analysis
4. Quality control analysis (cross-plots and statistics)
Enter choice (1, 2, 3, or 4): 2 

Generating enhanced visualization analysis for saits...
Creating separate Original vs Imputed and Original vs Predicted plots...
Creating cross-plot analysis for quality control...

Enhanced visualization analysis completed!
Separate plots and cross-plot analysis generated for detailed review

🔄 Step 11: Next action
What would you like to do next?
1. Exit the program
2. Continue processing (run another model)
3. Go back to plot options (Step 9)
Enter choice (1, 2, or 3): 2

🔄 Continuing to model selection...

Step 9: Running machine learning models...

Model selection options:
• Select multiple models by entering comma-separated numbers (e.g., 1,2,3)
• Type 'all' to select all available models
• Press Enter for default single model selection

📋 Well Log Model Selection Guide:
   Primary Imputation Models (filling missing values in existing sequences):
   1. SAITS - Best for complex geological patterns with long-range dependencies
   2. BRITS - Optimal for maintaining bidirectional geological continuity

   Primary Prediction Models (predicting log values in new wells):
   1. XGBoost - Excellent feature-target learning for cross-well prediction
   2. LightGBM - Fast and accurate for large-scale well log datasets
   3. CatBoost - Robust handling of mixed geological data types
   4. Transformer - Captures complex geological relationships across wells

   Versatile Models (both imputation and prediction):
   1. Transformer - Attention mechanism adapts to both tasks
   2. mRNN - Multi-scale processing handles both local and regional tasks
   3. Gradient Boosting Models - Consistent performance across both scenarios

Select model(s) to run
  1. xgboost
  2. lightgbm
  3. catboost
  4. linear_regression
  5. ridge_regression
  6. lasso_regression
  7. elastic_net
  8. saits
  9. brits
Default: ['xgboost']
Selection: 9
Selected models: brits

Running 1 model(s)...

--- Running Model 1/1: brits ---
🚀 [MAXIMUM PERFORMANCE] Starting optimized training for brits
   Expected speedup: 4-6x
   Optimization level: aggressive
   GPU strategy: modern_gpu
[MEM] Applying memory optimization with Maximum Performance
[MEM] Memory cleared
🎯 GPU Hardware: NVIDIA T550 Laptop GPU (Compute 7.5)
   ⚡ Mixed precision enabled for modern GPU
🚀 Attempting optimized pipeline (level: aggressive)
🚀 Starting Optimized Phase 1 Training (Level: aggressive)...
   Model: BRITS (Bidirectional RNN)
   Target: P-WAVE
   Features: ['GR', 'NPHI', 'RHOB', 'RT', 'TVDSS']
   📊 Dataset size: 57,856 rows, 8 wells (avg: 7232 rows/well)
   🔍 Pre-checking data sufficiency for aggressive optimization...
   📊 Wells analysis: 8/8 wells likely to pass (threshold: 11)
\n📊 Step 1: Optimized Data Preparation...
   Performing quick data quality assessment...
   📊 Small dataset detected (6,000 elements) - using adaptive quality threshold
   📈 Data quality score: 0.657 (finite rate: 66.1%)
   📊 Standard preprocessing required (score: 0.657 < 0.800)
Using enhanced normalization with winsorization...
Normalized 'GR': method=standard, valid_data=51983/57856 (89.8%)
Normalized 'NPHI': method=standard, valid_data=42700/57856 (73.8%)
Normalized 'RHOB': method=standard, valid_data=36835/57856 (63.7%)
Normalized 'RT': method=standard, valid_data=47696/57856 (82.4%)
Normalized 'TVDSS': method=standard, valid_data=55307/57856 (95.6%)
Normalized 'P-WAVE': method=standard, valid_data=37801/57856 (65.3%)
📊 Small dataset detected:
   - Total rows: 57856
   - Wells: 8
   - Avg rows/well: 7232.0
   - Valid data ratio: 6.4%
   - Recommended sequence length: 16
   - Recommended step: 16
🔧 Adjusted parameters for small dataset:
   - Sequence length: 64 → 16
   - Step: 1 → 16
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: TRAINING
Creating sequences | Current: EB-1: 100%|█████████████████████| 8/8 wells [00:00<00:00, 15.63well/s]    

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 23
   • Total sequences created: 2,177
Enhanced sequences shape: (2177, 16, 6), type: <class 'numpy.ndarray'>, dtype: float64
   Created 2177 sequences in 0.61s
\n🔍 Step 2: Vectorized Preprocessing Pipeline...
   🚀 Vectorized preprocessing: (2177, 16, 6)
   Vectorized preprocessing completed in 0.01s
\n🔧 Step 3: Smart Validation...
   Validation completed in 0.00s
   Sequences valid: ✅ STABLE
\n🎯 Step 4: Direct Tensor Training...
   📚 IMPUTATION MODE: Creating training sequences with missing values
Using enhanced missing value introduction with realistic patterns...
Introduced 20.1% missing values (41979 elements)
Pattern: 18809 random + 23170 chunked
Enhanced missing sequences shape: (2177, 16, 6), type: <class 'numpy.ndarray'>, dtype: float64
   Tensor preparation completed in 0.04s
   Training tensor shape: torch.Size([2177, 16, 6])
   Truth tensor shape: torch.Size([2177, 16, 6])
   Efficient synthetic DataFrame created: (100, 8)
--- Running Deep Learning Model: BRITS (Bidirectional RNN) ---
CuDNN benchmark enabled for performance.
Automatic well assignment:
  - Training/Validation wells: ['WELL_8', 'WELL_1', 'WELL_5', 'WELL_0', 'WELL_7', 'WELL_2', 'WELL_9', 'WELL_4']
  - Test wells: ['WELL_3', 'WELL_6']
   Using adaptive validation ratio: 15.0% (median well size: 10)
   🚨 Very small wells detected (min: 10) - reduced validation ratio to 10.0%
   Minimum well size threshold: 6 (validation ratio: 10.0%)
Flexible Split Report:
  - Wells for Training/Validation: 8
  - Wells for Final Testing: 2
  - Train Samples (Shallow part of train wells): 72
  - Validation Samples (Deeper part of train wells): 8
  - Test Samples (Entirely separate wells): 20

Running data leakage detection on splits...
🔍 Performing comprehensive data leakage check...
============================================================
🔍 Detecting perfect correlation leakage...
🚨 POTENTIAL DATA LEAKAGE DETECTED!
   TEST split:
     - RHOB: correlation = 0.984
🕐 Validating temporal split integrity...
✅ No temporal leakage detected in splits
🎯 Detecting target leakage in features...
✅ No target leakage detected in features
============================================================
📊 COMPREHENSIVE LEAKAGE CHECK SUMMARY
============================================================
🚨 DATA LEAKAGE DETECTED!
   Failed checks: 1/3
   Data quality score: 0.67

📋 All Recommendations:
   ⚠️ Suspiciously high correlations detected (>0.95)
   • Check if target information is leaking into features
   • Verify temporal ordering of data splits
   • Review feature engineering process
   • Consider removing highly correlated features
   ✅ No temporal leakage detected in splits
   ✅ No target leakage detected in features
============================================================
⚠️ Data leakage detected but continuing with training...
   Review the recommendations above to improve data quality.

Using enhanced normalization with winsorization...
Normalized 'GR': method=standard, valid_data=72/72 (100.0%)
Normalized 'NPHI': method=standard, valid_data=72/72 (100.0%)
Normalized 'RHOB': method=standard, valid_data=72/72 (100.0%)
Normalized 'RT': method=standard, valid_data=72/72 (100.0%)
Normalized 'TVDSS': method=standard, valid_data=72/72 (100.0%)
Normalized 'P-WAVE': method=standard, valid_data=72/72 (100.0%)
📊 Small dataset detected:
   - Total rows: 72
   - Wells: 8
   - Avg rows/well: 9.0
   - Valid data ratio: 100.0%
   - Recommended sequence length: 4
   - Recommended step: 1
🔧 Adjusted parameters for small dataset:
   - Sequence length: 64 → 4
   - Step: 1 → 1
   - Using relaxed validation for very small sequences
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: TRAINING
Creating sequences | Current: WELL_4: 100%|██████████████████| 8/8 wells [00:00<00:00, 395.42well/s]    

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 8
   • Total sequences created: 48
Enhanced sequences shape: (48, 4, 6), type: <class 'numpy.ndarray'>, dtype: float64
Using enhanced normalization with winsorization...
📊 Small dataset detected:
   - Total rows: 8
   - Wells: 8
   - Avg rows/well: 1.0
   - Valid data ratio: 100.0%
   - Recommended sequence length: 4
   - Recommended step: 1
🔧 Adjusted parameters for small dataset:
   - Sequence length: 64 → 4
   - Step: 1 → 1
   - Using relaxed validation for very small sequences
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: TRAINING
Creating sequences | Current: WELL_4: 100%|█████████████████| 8/8 wells [00:00<00:00, 1091.84well/s]    

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 0
   • Total sequences created: 0
Enhanced sequences shape: (0, 4, 6), type: <class 'numpy.ndarray'>, dtype: float32
⚠️ Enhanced sequence creation failed - falling back to standard method
   Enhanced result shape: (0, 4, 6) (expected: (n_sequences, 4, 6))
   Data quality check:
     - Total wells: 8
     - Total rows: 8
     - Feature columns: ['GR', 'NPHI', 'RHOB', 'RT', 'TVDSS', 'P-WAVE']
     - Sequence length: 4, step: 1
   🔄 Trying enhanced preprocessing with smaller sequence length: 2
Sequence creation mode: TRAINING
Creating sequences | Current: WELL_4: 100%|█████████████████| 8/8 wells [00:00<00:00, 1292.10well/s]    

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 0
   • Total sequences created: 0
   🔄 Trying enhanced preprocessing with smaller sequence length: 1
Sequence creation mode: TRAINING
Creating sequences | Current: WELL_4: 100%|█████████████████| 8/8 wells [00:00<00:00, 1124.63well/s]    

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 0
   • Total sequences created: 0
   🔄 Trying enhanced preprocessing with smaller sequence length: 8
Sequence creation mode: TRAINING
Creating sequences | Current: WELL_4: 100%|█████████████████| 8/8 wells [00:00<00:00, 1143.45well/s]    

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 0
   • Total sequences created: 0
   ⚠️ Enhanced preprocessing failed with all sequence lengths - falling back to standard method
Creating basic sequences | Current: WELL_9: 100%|████████████| 8/8 wells [00:00<00:00, 993.15well/s]    

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 8
   • Total sequences created: 0
Warning: No valid sequences could be created. Check data quality and sequence length.
📚 Introducing missingness for imputation training.
Using enhanced missing value introduction with realistic patterns...
❌ Optimized training failed: low >= high
   Falling back to original function...
--- Running Deep Learning Model: BRITS (Bidirectional RNN) ---
CuDNN benchmark enabled for performance.
Automatic well assignment:
  - Training/Validation wells: ['B-G-10', 'B-L-9', 'B-G-6', 'EB-1', 'B-L-1', 'B-L-6']
  - Test wells: ['B-L-2.G1', 'B-L-15']
   Using adaptive validation ratio: 30.0% (median well size: 7979)
   Minimum well size threshold: 12 (validation ratio: 30.0%)
Flexible Split Report:
  - Wells for Training/Validation: 6
  - Wells for Final Testing: 2
  - Train Samples (Shallow part of train wells): 32126
  - Validation Samples (Deeper part of train wells): 13772
  - Test Samples (Entirely separate wells): 11958

Running data leakage detection on splits...
🔍 Performing comprehensive data leakage check...
============================================================
🔍 Detecting perfect correlation leakage...
🚨 POTENTIAL DATA LEAKAGE DETECTED!
   TEST split:
     - TVDSS: correlation = 0.966
🕐 Validating temporal split integrity...
✅ No temporal leakage detected in splits
🎯 Detecting target leakage in features...
✅ No target leakage detected in features
============================================================
📊 COMPREHENSIVE LEAKAGE CHECK SUMMARY
============================================================
🚨 DATA LEAKAGE DETECTED!
   Failed checks: 1/3
   Data quality score: 0.67

📋 All Recommendations:
   ⚠️ Suspiciously high correlations detected (>0.95)
   • Check if target information is leaking into features
   • Verify temporal ordering of data splits
   • Review feature engineering process
   • Consider removing highly correlated features
   ✅ No temporal leakage detected in splits
   ✅ No target leakage detected in features
============================================================
⚠️ Data leakage detected but continuing with training...
   Review the recommendations above to improve data quality.

Using enhanced normalization with winsorization...
Normalized 'GR': method=standard, valid_data=31964/32126 (99.5%)
Normalized 'NPHI': method=standard, valid_data=24749/32126 (77.0%)
Normalized 'RHOB': method=standard, valid_data=20743/32126 (64.6%)
Normalized 'RT': method=standard, valid_data=31442/32126 (97.9%)
Normalized 'TVDSS': method=standard, valid_data=32126/32126 (100.0%)
Normalized 'P-WAVE': method=standard, valid_data=21526/32126 (67.0%)
📊 Small dataset detected:
   - Total rows: 32126
   - Wells: 6
   - Avg rows/well: 5354.3
   - Valid data ratio: 0.0%
   - Recommended sequence length: 16
   - Recommended step: 16
🔧 Adjusted parameters for small dataset:
   - Sequence length: 64 → 16
   - Step: 1 → 16
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: TRAINING
Creating sequences | Current: B-L-6: 100%|████████████████████| 6/6 wells [00:00<00:00, 21.53well/s]

📊 Well Processing Summary:
   • Total wells processed: 6
   • Successful: 6
   • Failed: 0
   • Total valid intervals: 15
   • Total sequences created: 1,179
Enhanced sequences shape: (1179, 16, 6), type: <class 'numpy.ndarray'>, dtype: float64
Using enhanced normalization with winsorization...
📊 Small dataset detected:
   - Total rows: 13772
   - Wells: 6
   - Avg rows/well: 2295.3
   - Valid data ratio: 0.0%
   - Recommended sequence length: 16
   - Recommended step: 16
🔧 Adjusted parameters for small dataset:
   - Sequence length: 64 → 16
   - Step: 1 → 16
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: TRAINING
Creating sequences | Current: B-L-6: 100%|████████████████████| 6/6 wells [00:00<00:00, 56.68well/s]    

📊 Well Processing Summary:
   • Total wells processed: 6
   • Successful: 6
   • Failed: 0
   • Total valid intervals: 6
   • Total sequences created: 391
Enhanced sequences shape: (391, 16, 6), type: <class 'numpy.ndarray'>, dtype: float64
📚 Introducing missingness for imputation training.
Using enhanced missing value introduction with realistic patterns...
Introduced 20.0% missing values (22595 elements)
Pattern: 10186 random + 12409 chunked
Enhanced missing sequences shape: (1179, 16, 6), type: <class 'numpy.ndarray'>, dtype: float64
Using enhanced missing value introduction with realistic patterns...
Introduced 19.9% missing values (7460 elements)
Pattern: 3378 random + 4082 chunked
Enhanced missing sequences shape: (391, 16, 6), type: <class 'numpy.ndarray'>, dtype: float64
Converting sequences to tensors...
   Processing train_sequences_missing: current type is <class 'numpy.ndarray'>
   Changing train_sequences_missing dtype from float64 to float32.
   train_sequences_missing tensor created with shape: torch.Size([1179, 16, 6]), dtype: torch.float32   
   Processing train_sequences_true: current type is <class 'numpy.ndarray'>
   Changing train_sequences_true dtype from float64 to float32.
   train_sequences_true tensor created with shape: torch.Size([1179, 16, 6]), dtype: torch.float32      
   Processing val_sequences_missing: current type is <class 'numpy.ndarray'>
   Changing val_sequences_missing dtype from float64 to float32.
   val_sequences_missing tensor created with shape: torch.Size([391, 16, 6]), dtype: torch.float32      
   Processing val_sequences_true: current type is <class 'numpy.ndarray'>
   Changing val_sequences_true dtype from float64 to float32.
   val_sequences_true tensor created with shape: torch.Size([391, 16, 6]), dtype: torch.float32
   Tensor shapes - Train: torch.Size([1179, 16, 6]), Truth: torch.Size([1179, 16, 6])
❌ Model creation failed: name 'train_sequences' is not defined
✅ Optimized pipeline completed successfully in 1.44s
[MEM] Memory cleared
✅ [MAXIMUM PERFORMANCE] Completed optimized training for brits with memory optimization
brits completed successfully

Batch execution summary:
   • Successful models: 1
   • Failed models: 0
   • Successful: brits
Multi-model execution completed

Step 10: Configure output options

Model Performance Summary:
----------------------------------------------------------------------
Model           MAE        R²         RMSE       Composite
----------------------------------------------------------------------
----------------------------------------------------------------------
No successful models to evaluate.

[Chart] Step 10: Select visualization and output options

Single model available: brits
1. Save results to files (with standard plots)
2. Enhanced visualization analysis (no file output)
3. Detailed single model analysis
4. Quality control analysis (cross-plots and statistics)
Enter choice (1, 2, 3, or 4): 4

[Chart] Generating quality control analysis for brits...

Quality control analysis completed!
Cross-plots, statistics, and quality metrics generated for review

🔄 Step 11: Next action
What would you like to do next?
1. Exit the program
2. Continue processing (run another model)
3. Go back to plot options (Step 9)
Enter choice (1, 2, or 3): 1

👋 Exiting program. Thank you for using ML Log Prediction!
(mwlt) PS C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\2_Pyth_Script\15_ML_Log_Prediction\branch_3_gpu_rk3_grok> & C:/Users/<USER>/mwlt/Scripts/python.exe "c:/Users/<USER>/OneDrive - PT Pertamina (Persero)/13_Python_PKB/2_Pyth_Script/15_ML_Log_Prediction/branch_3_gpu_rk3_grok/main.py"
[PHASE1] Setting up development environment...
🔇 Compilation warnings suppressed for cleaner output

==================================================
🔍 ENVIRONMENT DIAGNOSTICS
==================================================
✅ MSVC: MSVC compiler available
❌ Windows SDK: Not detected
   💡 Install Windows SDK for development headers
✅ CUDA: CUDA toolkit detected: Cuda compilation tools, release 11.8, V11.8.89

📋 Summary:
   ✅ Some development tools available
==================================================
   [INFO] Environment already initialized
[PHASE1] Configuring memory optimization environment...
   [OK] PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True
Multiple Linear Regression utilities loaded successfully
Data leakage detection module loaded
Loading advanced deep learning models...
WARNING:tensorflow:From C:\Users\<USER>\mwlt\lib\site-packages\keras\src\losses.py:2976: The name tf.losses.sparse_softmax_cross_entropy is deprecated. Please use tf.compat.v1.losses.sparse_softmax_cross_entropy instead.

🔧 TensorFlow environment configured for compatibility (TF 2.15.1 available)
✅ TensorFlow 2.15.1 loaded (CPU-only)
WARNING: Failed to find MSVC.
WARNING: Failed to find Windows SDK.
WARNING: Failed to find CUDA.

████████╗██╗███╗   ███╗███████╗    ███████╗███████╗██████╗ ██╗███████╗███████╗    █████╗ ██╗
╚══██╔══╝██║████╗ ████║██╔════╝    ██╔════╝██╔════╝██╔══██╗██║██╔════╝██╔════╝   ██╔══██╗██║
   ██║   ██║██╔████╔██║█████╗█████╗███████╗█████╗  ██████╔╝██║█████╗  ███████╗   ███████║██║
   ██║   ██║██║╚██╔╝██║██╔══╝╚════╝╚════██║██╔══╝  ██╔══██╗██║██╔══╝  ╚════██║   ██╔══██║██║
   ██║   ██║██║ ╚═╝ ██║███████╗    ███████║███████╗██║  ██║██║███████╗███████║██╗██║  ██║██║
   ╚═╝   ╚═╝╚═╝     ╚═╝╚══════╝    ╚══════╝╚══════╝╚═╝  ╚═╝╚═╝╚══════╝╚══════╝╚═╝╚═╝  ╚═╝╚═╝
ai4ts v0.0.3 - building AI for unified time-series analysis, https://time-series.ai

✅ PyPOTS imported successfully with TensorFlow backend
✅ PyPOTS components loaded successfully for SAITS model
SAITS model loaded successfully
🔄 TensorFlow cache reset - will re-check availability
🔧 TensorFlow environment configured for compatibility (TF 2.15.1 available)
✅ TensorFlow 2.15.1 loaded (CPU-only)
✅ PyPOTS imported successfully with TensorFlow backend
✅ PyPOTS components loaded successfully for BRITS model
BRITS model loaded successfully
Advanced models loaded: ['saits', 'brits']
Total available: 2/5 models
Advanced models module initialized (Phase 1 foundation)
Ready for Phase 2: Core model implementations
Advanced deep learning models module loaded
Available advanced models: ['saits', 'brits']
SAITS model added to registry
BRITS model added to registry
Enhanced MODEL_REGISTRY with 2 advanced models
Available advanced models: ['saits', 'brits']
   [OK] Phase 1 Enhanced Deep Learning Integration loaded (ml_core_phase1_integration)
   [OK] Optimized pipeline functions available
INFO:utils.display_utils:Configuring fonts for Windows system
INFO:utils.display_utils:Set matplotlib font to: Segoe UI Emoji
INFO:utils.display_utils:Emoji support confirmed
INFO:utils.display_utils:Configured warning filters for font issues
🔍 Performance Monitor initialized
   • GPU monitoring enabled
   • Monitoring interval: 1.0s
[MEM] Environment configured for memory optimization
[MEM] Memory Optimizer initialized
   * Mixed precision enabled
   * Memory monitoring enabled
   [OK] Memory optimizer initialized
============================================================
 ML LOG PREDICTION
============================================================

[MEM] Initial Memory Status:

==================================================
[MEM] MEMORY STATUS
==================================================
System Memory:
   * Total: 31.7 GB
   * Available: 12.8 GB
   * Usage: 59.6%

GPU Memory:
   * Total: 4.0 GB
   * Allocated: 0.0 GB
   * Reserved: 0.0 GB
   * Free: 4.0 GB
==================================================

Step 1: Select LAS files
Select LAS files using the file dialog...
Selected 8 LAS files:
  1. B-G-6_RP_INPUT.las
  2. B-G-10_RP_INPUT.las
  3. B-L-1_RP_INPUT.las
  4. B-L-2.G1_RP_INPUT.las
  5. B-L-6_RP_INPUT.las
  6. B-L-9_RP_INPUT.las
  7. B-L-15_RP_INPUT.las
  8. EB-1_RP_INPUT.las

Step 2: Loading LAS files...
INFO:lasio.reader:ascii was found by ad hoc to work but note it might not be the correct encoding       
INFO:lasio.reader:Opening C:/Users/<USER>/OneDrive - PT Pertamina (Persero)/13_Python_PKB/2_Pyth_Script/15_ML_Log_Prediction/branch_3_gpu/Las/B-G-6_RP_INPUT.las as ascii and treating errors with "replace"
INFO:lasio.las:The read substitutions ['run-on(-)']have been removed as this file appears to contain hyphens.
Loaded B-G-6
INFO:lasio.reader:ascii was found by ad hoc to work but note it might not be the correct encoding       
INFO:lasio.reader:Opening C:/Users/<USER>/OneDrive - PT Pertamina (Persero)/13_Python_PKB/2_Pyth_Script/15_ML_Log_Prediction/branch_3_gpu/Las/B-G-10_RP_INPUT.las as ascii and treating errors with "replace"
INFO:lasio.las:The read substitutions ['run-on(-)']have been removed as this file appears to contain hyphens.
Loaded B-G-10
INFO:lasio.reader:ascii was found by ad hoc to work but note it might not be the correct encoding       
INFO:lasio.reader:Opening C:/Users/<USER>/OneDrive - PT Pertamina (Persero)/13_Python_PKB/2_Pyth_Script/15_ML_Log_Prediction/branch_3_gpu/Las/B-L-1_RP_INPUT.las as ascii and treating errors with "replace"
INFO:lasio.las:The read substitutions ['run-on(-)']have been removed as this file appears to contain hyphens.
Loaded B-L-1
INFO:lasio.reader:ascii was found by ad hoc to work but note it might not be the correct encoding       
INFO:lasio.reader:Opening C:/Users/<USER>/OneDrive - PT Pertamina (Persero)/13_Python_PKB/2_Pyth_Script/15_ML_Log_Prediction/branch_3_gpu/Las/B-L-2.G1_RP_INPUT.las as ascii and treating errors with "replace"
INFO:lasio.las:The read substitutions ['run-on(-)']have been removed as this file appears to contain hyphens.
Loaded B-L-2.G1
INFO:lasio.reader:ascii was found by ad hoc to work but note it might not be the correct encoding       
INFO:lasio.reader:Opening C:/Users/<USER>/OneDrive - PT Pertamina (Persero)/13_Python_PKB/2_Pyth_Script/15_ML_Log_Prediction/branch_3_gpu/Las/B-L-6_RP_INPUT.las as ascii and treating errors with "replace"
INFO:lasio.las:The read substitutions ['run-on(-)']have been removed as this file appears to contain hyphens.
Loaded B-L-6
INFO:lasio.reader:ascii was found by ad hoc to work but note it might not be the correct encoding       
INFO:lasio.reader:Opening C:/Users/<USER>/OneDrive - PT Pertamina (Persero)/13_Python_PKB/2_Pyth_Script/15_ML_Log_Prediction/branch_3_gpu/Las/B-L-9_RP_INPUT.las as ascii and treating errors with "replace"
INFO:lasio.las:The read substitutions ['run-on(-)']have been removed as this file appears to contain hyphens.
Loaded B-L-9
INFO:lasio.reader:ascii was found by ad hoc to work but note it might not be the correct encoding       
INFO:lasio.reader:Opening C:/Users/<USER>/OneDrive - PT Pertamina (Persero)/13_Python_PKB/2_Pyth_Script/15_ML_Log_Prediction/branch_3_gpu/Las/B-L-15_RP_INPUT.las as ascii and treating errors with "replace"
INFO:lasio.las:The read substitutions ['run-on(-)']have been removed as this file appears to contain hyphens.
Loaded B-L-15
INFO:lasio.reader:ascii was found by ad hoc to work but note it might not be the correct encoding       
INFO:lasio.reader:Opening C:/Users/<USER>/OneDrive - PT Pertamina (Persero)/13_Python_PKB/2_Pyth_Script/15_ML_Log_Prediction/branch_3_gpu/Las/EB-1_RP_INPUT.las as ascii and treating errors with "replace"
INFO:lasio.las:The read substitutions ['run-on(-)']have been removed as this file appears to contain hyphens.
Loaded EB-1
Successfully loaded:
   • 57856 data points
   • 8 wells: B-G-10, B-G-6, B-L-1, B-L-15, B-L-2.G1, B-L-6, B-L-9, EB-1
   • 41 log curves: CALI, DENS_WET, DT, DTS, FACIES_INTERPRETATION_2021, FLUID_CODE, FLUID_REINTERPRETATION_2021, GDRY, GR, GSAT, GSOLID, KDRY, KFLUID, KSAT, KSAT_WET, KSOLID, LITHO_CODE, NPHI, P-IMPEDANCE_TRANS, P-WAVE, PHID, PHIE, PHIE_COREL, PHIT, POISSON'S_RATIO_TRANS, PVEL_WET, RHOB, RHOFLUID, RHOSOLID, RT, S-IMPEDANCE_TRANS, S-WAVE, SEIS_DEPTH, SQ_VSVP_RATIO, SVEL_WET, SWE, SWT, TVDSS, VOL_WETCLAY, VPVS_RATIO_TRANS, VSVP_RATIO_TRANS

Step 3: Configure feature and target logs

Select feature logs (comma separated indexes)
  1. CALI
  2. DENS_WET
  3. DT
  4. DTS
  5. FACIES_INTERPRETATION_2021
  6. FLUID_CODE
  7. FLUID_REINTERPRETATION_2021
  8. GDRY
  9. GR
  10. GSAT
  11. GSOLID
  12. KDRY
  13. KFLUID
  14. KSAT
  15. KSAT_WET
  16. KSOLID
  17. LITHO_CODE
  18. NPHI
  19. P-IMPEDANCE_TRANS
  20. P-WAVE
  21. PHID
  22. PHIE
  23. PHIE_COREL
  24. PHIT
  25. POISSON'S_RATIO_TRANS
  26. PVEL_WET
  27. RHOB
  28. RHOFLUID
  29. RHOSOLID
  30. RT
  31. S-IMPEDANCE_TRANS
  32. S-WAVE
  33. SEIS_DEPTH
  34. SQ_VSVP_RATIO
  35. SVEL_WET
  36. SWE
  37. SWT
  38. TVDSS
  39. VOL_WETCLAY
  40. VPVS_RATIO_TRANS
  41. VSVP_RATIO_TRANS
Default: ['CALI', 'DENS_WET', 'DT', 'DTS']
Selection: 9, 18, 27, 30, 38

Select target log
  1. CALI
  2. DENS_WET
  3. DT
  4. DTS
  5. FACIES_INTERPRETATION_2021
  6. FLUID_CODE
  7. FLUID_REINTERPRETATION_2021
  8. GDRY
  9. GSAT
  10. GSOLID
  11. KDRY
  12. KFLUID
  13. KSAT
  14. KSAT_WET
  15. KSOLID
  16. LITHO_CODE
  17. P-IMPEDANCE_TRANS
  18. P-WAVE
  19. PHID
  20. PHIE
  21. PHIE_COREL
  22. PHIT
  23. POISSON'S_RATIO_TRANS
  24. PVEL_WET
  25. RHOFLUID
  26. RHOSOLID
  27. S-IMPEDANCE_TRANS
  28. S-WAVE
  29. SEIS_DEPTH
  30. SQ_VSVP_RATIO
  31. SVEL_WET
  32. SWE
  33. SWT
  34. VOL_WETCLAY
  35. VPVS_RATIO_TRANS
  36. VSVP_RATIO_TRANS
Default: CALI
Selection: 18
Feature logs: GR, NPHI, RHOB, RT, TVDSS
Target log: P-WAVE

Step 4: Configure training/prediction strategy

Training/prediction mode?
  1. mixed
  2. separated
Default: mixed
Selection: 1
Mode: mixed

Step 5: Configure prediction mode

Prediction mode: 1 fill-missing, 2 CV, 3 full
  1. 1
  2. 2
  3. 3
Default: 1
Selection: 3
Prediction mode: 3

Step 6: Configure deep learning pipeline optimization

🚀 Deep Learning Pipeline Configuration
============================================================
Choose the training pipeline for SAITS/BRITS models:

1. 🔥 Optimized Phase 1 Pipeline (RECOMMENDED)
   • 3-4x faster training with moderate optimization
   • Advanced preprocessing and validation
   • GPU-accelerated operations when available
   • Automatic fallback to original pipeline if issues occur

2. 📚 Original Training Pipeline
   • Standard training path from ml_core.py
   • Proven stability and compatibility
   • No additional optimizations

3. ⚡ Maximum Performance Pipeline (EXPERIMENTAL)
   • 4-6x faster training with aggressive optimization
   • GPU preprocessing and tensor operations
   • Best for large datasets and modern hardware

Select pipeline (1-3) [1]: 2

🎯 GPU Detected: NVIDIA T550 Laptop GPU
   Compute Capability: 7.5
   ✅ Mixed precision training supported
   ✅ GPU preprocessing enabled

✅ Pipeline Configuration:
   • Pipeline: Original
   • Expected speedup: 1x (baseline)
   • GPU strategy: modern_gpu

Step 7: Configure model hyperparameters
   ⚡ saits: Mixed precision enabled for modern GPU
   ⚡ brits: Mixed precision enabled for modern GPU
✅ Hyperparameters configured with GPU optimizations

Step 8: Data cleaning and quality control

Coverage:
  GR: 89.8%
  NPHI: 73.8%
  RHOB: 63.7%
  RT: 82.4%
  TVDSS: 95.6%
  P-WAVE: 65.3%

Step 8.5: Data sufficiency optimization

📊 Data Sufficiency Analysis:
   • Total wells: 8
   • Median well size: 7832 rows
   • Small wells (< 50 rows): 0.0%
   • Optimal sequence length: 64
✅ Hyperparameters optimized for data sufficiency

Step 9: Running machine learning models...

Model selection options:
• Select multiple models by entering comma-separated numbers (e.g., 1,2,3)
• Type 'all' to select all available models
• Press Enter for default single model selection

📋 Well Log Model Selection Guide:
   Primary Imputation Models (filling missing values in existing sequences):
   1. SAITS - Best for complex geological patterns with long-range dependencies
   2. BRITS - Optimal for maintaining bidirectional geological continuity

   Primary Prediction Models (predicting log values in new wells):
   1. XGBoost - Excellent feature-target learning for cross-well prediction
   2. LightGBM - Fast and accurate for large-scale well log datasets
   3. CatBoost - Robust handling of mixed geological data types
   4. Transformer - Captures complex geological relationships across wells

   Versatile Models (both imputation and prediction):
   1. Transformer - Attention mechanism adapts to both tasks
   2. mRNN - Multi-scale processing handles both local and regional tasks
   3. Gradient Boosting Models - Consistent performance across both scenarios

Select model(s) to run
  1. xgboost
  2. lightgbm
  3. catboost
  4. linear_regression
  5. ridge_regression
  6. lasso_regression
  7. elastic_net
  8. saits
  9. brits
Default: ['xgboost']
Selection: 8
Selected models: saits

Running 1 model(s)...

--- Running Model 1/1: saits ---
📚 [ORIGINAL] Starting standard training for saits
   Using original ml_core.py pipeline
[MEM] Starting memory-optimized training for saits
[MEM] Memory cleared
--- Running Deep Learning Model: SAITS (Self-Attention) ---
CuDNN benchmark enabled for performance.
Automatic well assignment:
  - Training/Validation wells: ['B-G-10', 'B-L-9', 'B-G-6', 'EB-1', 'B-L-1', 'B-L-6']
  - Test wells: ['B-L-2.G1', 'B-L-15']
   Using adaptive validation ratio: 30.0% (median well size: 7979)
   Minimum well size threshold: 12 (validation ratio: 30.0%)
Flexible Split Report:
  - Wells for Training/Validation: 6
  - Wells for Final Testing: 2
  - Train Samples (Shallow part of train wells): 32126
  - Validation Samples (Deeper part of train wells): 13772
  - Test Samples (Entirely separate wells): 11958

Running data leakage detection on splits...
🔍 Performing comprehensive data leakage check...
============================================================
🔍 Detecting perfect correlation leakage...
🚨 POTENTIAL DATA LEAKAGE DETECTED!
   TEST split:
     - TVDSS: correlation = 0.966
🕐 Validating temporal split integrity...
✅ No temporal leakage detected in splits
🎯 Detecting target leakage in features...
✅ No target leakage detected in features
============================================================
📊 COMPREHENSIVE LEAKAGE CHECK SUMMARY
============================================================
🚨 DATA LEAKAGE DETECTED!
   Failed checks: 1/3
   Data quality score: 0.67

📋 All Recommendations:
   ⚠️ Suspiciously high correlations detected (>0.95)
   • Check if target information is leaking into features
   • Verify temporal ordering of data splits
   • Review feature engineering process
   • Consider removing highly correlated features
   ✅ No temporal leakage detected in splits
   ✅ No target leakage detected in features
============================================================
⚠️ Data leakage detected but continuing with training...
   Review the recommendations above to improve data quality.

Using enhanced normalization with winsorization...
Normalized 'GR': method=standard, valid_data=31964/32126 (99.5%)
Normalized 'NPHI': method=standard, valid_data=24749/32126 (77.0%)
Normalized 'RHOB': method=standard, valid_data=20743/32126 (64.6%)
Normalized 'RT': method=standard, valid_data=31442/32126 (97.9%)
Normalized 'TVDSS': method=standard, valid_data=32126/32126 (100.0%)
Normalized 'P-WAVE': method=standard, valid_data=21526/32126 (67.0%)
📊 Small dataset detected:
   - Total rows: 32126
   - Wells: 6
   - Avg rows/well: 5354.3
   - Valid data ratio: 0.0%
   - Recommended sequence length: 16
   - Recommended step: 16
🔧 Adjusted parameters for small dataset:
   - Sequence length: 64 → 16
   - Step: 1 → 16
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: TRAINING
Creating sequences | Current: B-L-6: 100%|████████████████████| 6/6 wells [00:00<00:00, 20.74well/s]    

📊 Well Processing Summary:
   • Total wells processed: 6
   • Successful: 6
   • Failed: 0
   • Total valid intervals: 15
   • Total sequences created: 1,179
Enhanced sequences shape: (1179, 16, 6), type: <class 'numpy.ndarray'>, dtype: float64
Using enhanced normalization with winsorization...
📊 Small dataset detected:
   - Total rows: 13772
   - Wells: 6
   - Avg rows/well: 2295.3
   - Valid data ratio: 0.0%
   - Recommended sequence length: 16
   - Recommended step: 16
🔧 Adjusted parameters for small dataset:
   - Sequence length: 64 → 16
   - Step: 1 → 16
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: TRAINING
Creating sequences | Current: B-L-6: 100%|████████████████████| 6/6 wells [00:00<00:00, 56.58well/s]    

📊 Well Processing Summary:
   • Total wells processed: 6
   • Successful: 6
   • Failed: 0
   • Total valid intervals: 6
   • Total sequences created: 391
Enhanced sequences shape: (391, 16, 6), type: <class 'numpy.ndarray'>, dtype: float64
📚 Introducing missingness for imputation training.
Using enhanced missing value introduction with realistic patterns...
Introduced 20.0% missing values (22595 elements)
Pattern: 10186 random + 12409 chunked
Enhanced missing sequences shape: (1179, 16, 6), type: <class 'numpy.ndarray'>, dtype: float64
Using enhanced missing value introduction with realistic patterns...
Introduced 19.9% missing values (7460 elements)
Pattern: 3378 random + 4082 chunked
Enhanced missing sequences shape: (391, 16, 6), type: <class 'numpy.ndarray'>, dtype: float64
Converting sequences to tensors...
   Processing train_sequences_missing: current type is <class 'numpy.ndarray'>
   Changing train_sequences_missing dtype from float64 to float32.
   train_sequences_missing tensor created with shape: torch.Size([1179, 16, 6]), dtype: torch.float32   
   Processing train_sequences_true: current type is <class 'numpy.ndarray'>
   Changing train_sequences_true dtype from float64 to float32.
   train_sequences_true tensor created with shape: torch.Size([1179, 16, 6]), dtype: torch.float32      
   Processing val_sequences_missing: current type is <class 'numpy.ndarray'>
   Changing val_sequences_missing dtype from float64 to float32.
   val_sequences_missing tensor created with shape: torch.Size([391, 16, 6]), dtype: torch.float32      
   Processing val_sequences_true: current type is <class 'numpy.ndarray'>
   Changing val_sequences_true dtype from float64 to float32.
   val_sequences_true tensor created with shape: torch.Size([391, 16, 6]), dtype: torch.float32
   Tensor shapes - Train: torch.Size([1179, 16, 6]), Truth: torch.Size([1179, 16, 6])
❌ Model creation failed: name 'train_sequences' is not defined
[MEM] Memory cleared
✅ [ORIGINAL] Completed training for saits with memory optimization
saits completed successfully

Batch execution summary:
   • Successful models: 1
   • Failed models: 0
   • Successful: saits
Multi-model execution completed

Step 10: Configure output options

Model Performance Summary:
----------------------------------------------------------------------
Model           MAE        R²         RMSE       Composite
----------------------------------------------------------------------
----------------------------------------------------------------------
No successful models to evaluate.

[Chart] Step 10: Select visualization and output options

Single model available: saits
1. Save results to files (with standard plots)
2. Enhanced visualization analysis (no file output)
3. Detailed single model analysis
4. Quality control analysis (cross-plots and statistics)
Enter choice (1, 2, 3, or 4):