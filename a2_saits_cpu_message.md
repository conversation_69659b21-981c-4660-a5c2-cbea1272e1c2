Running 1 model(s)...

--- Running Model 1/1: saits ---
🚀 [MAXIMUM PERFORMANCE] Starting optimized training for saits
   Expected speedup: 4-6x
   Optimization level: aggressive
   GPU strategy: modern_gpu
[MEM] Applying memory optimization with Maximum Performance
[MEM] Memory cleared
🎯 GPU Hardware: NVIDIA T550 Laptop GPU (Compute 7.5)
   ⚡ Mixed precision enabled for modern GPU
🚀 Attempting optimized pipeline (level: aggressive)
🚀 Starting Optimized Phase 1 Training (Level: aggressive)...
   Model: SAITS (Self-Attention)
   Target: P-WAVE
   Features: ['GR', 'NPHI', 'RHOB', 'RT', 'TVDSS']
   📊 Dataset size: 57,856 rows, 8 wells (avg: 7232 rows/well)
   🔍 Pre-checking data sufficiency for aggressive optimization...
   📊 Wells analysis: 8/8 wells likely to pass (threshold: 11)
\n📊 Step 1: Optimized Data Preparation...
   Performing quick data quality assessment...
   📊 Small dataset detected (6,000 elements) - using adaptive quality threshold
   📈 Data quality score: 0.657 (finite rate: 66.1%)
   📊 Standard preprocessing required (score: 0.657 < 0.800)
Using enhanced normalization with winsorization...
Normalized 'GR': method=standard, valid_data=51983/57856 (89.8%)
Normalized 'NPHI': method=standard, valid_data=42700/57856 (73.8%)
Normalized 'RHOB': method=standard, valid_data=36835/57856 (63.7%)
Normalized 'RT': method=standard, valid_data=47696/57856 (82.4%)
Normalized 'TVDSS': method=standard, valid_data=55307/57856 (95.6%)
Normalized 'P-WAVE': method=standard, valid_data=37801/57856 (65.3%)
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: TRAINING
Creating sequences:   0%|                                             | 0/8 wells [00:00<?, ?wellCreating sequences | Current: B-G-6:   0%|                            | 0/8 wells [00:00<?, ?wellCreating sequences | Current: B-G-6:  12%|██▌                 | 1/8 wells [00:01<00:07,  1.05s/weCreating sequences | Current: B-G-6:  12%|██▌                 | 1/8 wells [00:01<00:07,  1.05s/weCreating sequences | Current: B-G-10:  12%|██▍                | 1/8 wells [00:01<00:07,  1.05s/weCreating sequences | Current: B-G-10:  25%|████▊              | 2/8 wells [00:02<00:06,  1.05s/weCreating sequences | Current: B-G-10:  25%|████▊              | 2/8 wells [00:02<00:06,  1.05s/weCreating sequences | Current: B-L-1:  25%|█████               | 2/8 wells [00:02<00:06,  1.05s/weCreating sequences | Current: B-L-1:  38%|███████▌            | 3/8 wells [00:02<00:04,  1.05wellCreating sequences | Current: B-L-1:  38%|███████▌            | 3/8 wells [00:02<00:04,  1.05wellCreating sequences | Current: B-L-2.G1:  38%|██████▍          | 3/8 wells [00:02<00:04,  1.05wellCreating sequences | Current: B-L-2.G1:  50%|████████▌        | 4/8 wells [00:04<00:04,  1.05s/weCreating sequences | Current: B-L-2.G1:  50%|████████▌        | 4/8 wells [00:04<00:04,  1.05s/weCreating sequences | Current: B-L-6:  50%|██████████          | 4/8 wells [00:04<00:04,  1.05s/weCreating sequences | Current: B-L-6:  62%|████████████▌       | 5/8 wells [00:04<00:02,  1.13wellCreating sequences | Current: B-L-6:  62%|████████████▌       | 5/8 wells [00:04<00:02,  1.13wellCreating sequences | Current: B-L-9:  62%|████████████▌       | 5/8 wells [00:04<00:02,  1.13wellCreating sequences | Current: B-L-9:  75%|███████████████     | 6/8 wells [00:05<00:01,  1.18wellCreating sequences | Current: B-L-9:  75%|███████████████     | 6/8 wells [00:05<00:01,  1.18wellCreating sequences | Current: B-L-15:  75%|██████████████▎    | 6/8 wells [00:05<00:01,  1.18wellCreating sequences | Current: B-L-15:  88%|████████████████▋  | 7/8 wells [00:06<00:00,  1.18wellCreating sequences | Current: B-L-15:  88%|████████████████▋  | 7/8 wells [00:06<00:00,  1.18wellCreating sequences | Current: EB-1:  88%|██████████████████▍  | 7/8 wells [00:06<00:00,  1.18wellCreating sequences | Current: EB-1: 100%|█████████████████████| 8/8 wells [00:07<00:00,  1.07wellCreating sequences | Current: EB-1: 100%|█████████████████████| 8/8 wells [00:07<00:00,  1.07wellCreating sequences | Current: EB-1: 100%|█████████████████████| 8/8 wells [00:07<00:00,  1.07well/s]

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 23
   • Total sequences created: 33,543
Enhanced sequences shape: (33543, 64, 6), type: <class 'numpy.ndarray'>, dtype: float64
   Created 33543 sequences in 7.59s
\n🔍 Step 2: Vectorized Preprocessing Pipeline...
   🚀 Vectorized preprocessing: (33543, 64, 6)
   Vectorized preprocessing completed in 0.39s
\n🔧 Step 3: Smart Validation...
   Validation completed in 0.02s
   Sequences valid: ✅ STABLE
\n🎯 Step 4: Direct Tensor Training...
   📚 IMPUTATION MODE: Creating training sequences with missing values
Using enhanced missing value introduction with realistic patterns...
Introduced 26.0% missing values (3354999 elements)
Pattern: 1159245 random + 2195754 chunked
Enhanced missing sequences shape: (33543, 64, 6), type: <class 'numpy.ndarray'>, dtype: float64
   Tensor preparation completed in 2.91s
   Training tensor shape: torch.Size([33543, 64, 6])
   Truth tensor shape: torch.Size([33543, 64, 6])
   Efficient synthetic DataFrame created: (100, 8)
--- Running Deep Learning Model: SAITS (Self-Attention) ---
CuDNN benchmark enabled for performance.
Automatic well assignment:
  - Training/Validation wells: ['WELL_8', 'WELL_1', 'WELL_5', 'WELL_0', 'WELL_7', 'WELL_2', 'WELL_9', 'WELL_4']
  - Test wells: ['WELL_3', 'WELL_6']
   Using adaptive validation ratio: 15.0% (median well size: 10)
   🚨 Very small wells detected (min: 10) - reduced validation ratio to 10.0%
   Minimum well size threshold: 6 (validation ratio: 10.0%)
Flexible Split Report:
  - Wells for Training/Validation: 8
  - Wells for Final Testing: 2
  - Train Samples (Shallow part of train wells): 72
  - Validation Samples (Deeper part of train wells): 8
  - Test Samples (Entirely separate wells): 20

Running data leakage detection on splits...
🔍 Performing comprehensive data leakage check...
============================================================
🔍 Detecting perfect correlation leakage...
🚨 POTENTIAL DATA LEAKAGE DETECTED!
   TRAIN split:
     - NPHI: correlation = 0.959
     - RHOB: correlation = 0.994
     - RT: correlation = 1.000
     - TVDSS: correlation = 1.000
🕐 Validating temporal split integrity...
✅ No temporal leakage detected in splits
🎯 Detecting target leakage in features...
🚨 TARGET LEAKAGE DETECTED!
   Suspicious correlations:
     - RHOB: 0.9923
     - RT: 0.9996
     - TVDSS: 0.9999
============================================================
📊 COMPREHENSIVE LEAKAGE CHECK SUMMARY
============================================================
🚨 DATA LEAKAGE DETECTED!
   Failed checks: 2/3
   Data quality score: 0.33

📋 All Recommendations:
   ⚠️ Suspiciously high correlations detected (>0.95)
   • Check if target information is leaking into features
   • Verify temporal ordering of data splits
   • Review feature engineering process
   • Consider removing highly correlated features
   ✅ No temporal leakage detected in splits
   🚨 Target leakage detected in features!
   • Remove features that are identical to target
   • Check feature engineering pipeline for target contamination
   • Verify that future target values are not used as features
   • Review data preprocessing steps
============================================================
⚠️ Data leakage detected but continuing with training...
   Review the recommendations above to improve data quality.

Using enhanced normalization with winsorization...
Normalized 'GR': method=standard, valid_data=72/72 (100.0%)
Normalized 'NPHI': method=standard, valid_data=72/72 (100.0%)
Normalized 'RHOB': method=standard, valid_data=72/72 (100.0%)
Normalized 'RT': method=standard, valid_data=72/72 (100.0%)
Normalized 'TVDSS': method=standard, valid_data=72/72 (100.0%)
Normalized 'P-WAVE': method=standard, valid_data=72/72 (100.0%)
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: TRAINING
Creating sequences:   0%|                                             | 0/8 wells [00:00<?, ?wellCreating sequences | Current: WELL_8:   0%|                           | 0/8 wells [00:00<?, ?wellCreating sequences | Current: WELL_8:  12%|██▎               | 1/8 wells [00:00<00:00, 994.15wellCreating sequences | Current: WELL_1:  12%|██▎               | 1/8 wells [00:00<00:00, 994.15wellCreating sequences | Current: WELL_1:  25%|████▌             | 2/8 wells [00:00<00:00, 996.75wellCreating sequences | Current: WELL_5:  25%|████▌             | 2/8 wells [00:00<00:00, 996.75wellCreating sequences | Current: WELL_5:  38%|██████▊           | 3/8 wells [00:00<00:00, 997.54wellCreating sequences | Current: WELL_0:  38%|██████▊           | 3/8 wells [00:00<00:00, 997.54wellCreating sequences | Current: WELL_0:  50%|█████████         | 4/8 wells [00:00<00:00, 997.99wellCreating sequences | Current: WELL_7:  50%|█████████         | 4/8 wells [00:00<00:00, 997.99wellCreating sequences | Current: WELL_7:  62%|███████████▎      | 5/8 wells [00:00<00:00, 975.92wellCreating sequences | Current: WELL_2:  62%|███████████▎      | 5/8 wells [00:00<00:00, 975.92wellCreating sequences | Current: WELL_2:  75%|█████████████▌    | 6/8 wells [00:00<00:00, 935.71wellCreating sequences | Current: WELL_9:  75%|█████████████▌    | 6/8 wells [00:00<00:00, 935.71wellCreating sequences | Current: WELL_9:  88%|███████████████▊  | 7/8 wells [00:00<00:00, 943.15wellCreating sequences | Current: WELL_4:  88%|███████████████▊  | 7/8 wells [00:00<00:00, 943.15wellCreating sequences | Current: WELL_4: 100%|██████████████████| 8/8 wells [00:00<00:00, 950.17wellCreating sequences | Current: WELL_4: 100%|██████████████████| 8/8 wells [00:00<00:00, 950.17well/s]

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 8
   • Total sequences created: 0
Enhanced sequences shape: (0, 64, 6), type: <class 'numpy.ndarray'>, dtype: float32
⚠️ Enhanced sequence creation failed - falling back to standard method
   Enhanced result shape: (0, 64, 6) (expected: (n_sequences, 64, 6))
   Data quality check:
     - Total wells: 8
     - Total rows: 72
     - Feature columns: ['GR', 'NPHI', 'RHOB', 'RT', 'TVDSS', 'P-WAVE']
     - Sequence length: 64, step: 1
   🔄 Trying enhanced preprocessing with smaller sequence length: 32
Sequence creation mode: TRAINING
Creating sequences:   0%|                                             | 0/8 wells [00:00<?, ?wellCreating sequences | Current: WELL_8:   0%|                           | 0/8 wells [00:00<?, ?wellCreating sequences | Current: WELL_8:  12%|███▍                       | 1/8 wells [00:00<?, ?wellCreating sequences | Current: WELL_1:  12%|███▍                       | 1/8 wells [00:00<?, ?wellCreating sequences | Current: WELL_1:  25%|████▎            | 2/8 wells [00:00<00:00, 1730.32wellCreating sequences | Current: WELL_5:  25%|████▎            | 2/8 wells [00:00<00:00, 1730.32wellCreating sequences | Current: WELL_5:  38%|██████▍          | 3/8 wells [00:00<00:00, 1319.93wellCreating sequences | Current: WELL_0:  38%|██████▍          | 3/8 wells [00:00<00:00, 1319.93wellCreating sequences | Current: WELL_0:  50%|████████▌        | 4/8 wells [00:00<00:00, 1222.56wellCreating sequences | Current: WELL_7:  50%|████████▌        | 4/8 wells [00:00<00:00, 1222.56wellCreating sequences | Current: WELL_7:  62%|██████████▋      | 5/8 wells [00:00<00:00, 1187.45wellCreating sequences | Current: WELL_2:  62%|██████████▋      | 5/8 wells [00:00<00:00, 1187.45wellCreating sequences | Current: WELL_2:  75%|████████████▊    | 6/8 wells [00:00<00:00, 1103.86wellCreating sequences | Current: WELL_9:  75%|████████████▊    | 6/8 wells [00:00<00:00, 1103.86wellCreating sequences | Current: WELL_9:  88%|██████████████▉  | 7/8 wells [00:00<00:00, 1287.84wellCreating sequences | Current: WELL_4:  88%|██████████████▉  | 7/8 wells [00:00<00:00, 1287.84wellCreating sequences | Current: WELL_4: 100%|█████████████████| 8/8 wells [00:00<00:00, 1471.81wellCreating sequences | Current: WELL_4: 100%|█████████████████| 8/8 wells [00:00<00:00, 1471.81well/s]

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 8
   • Total sequences created: 0
   🔄 Trying enhanced preprocessing with smaller sequence length: 16
Sequence creation mode: TRAINING
Creating sequences:   0%|                                             | 0/8 wells [00:00<?, ?wellCreating sequences | Current: WELL_8:   0%|                           | 0/8 wells [00:00<?, ?wellCreating sequences | Current: WELL_8:  12%|██▎               | 1/8 wells [00:00<00:00, 717.96wellCreating sequences | Current: WELL_1:  12%|██▎               | 1/8 wells [00:00<00:00, 717.96wellCreating sequences | Current: WELL_1:  25%|████▌             | 2/8 wells [00:00<00:00, 656.23wellCreating sequences | Current: WELL_5:  25%|████▌             | 2/8 wells [00:00<00:00, 599.70wellCreating sequences | Current: WELL_5:  38%|██████▊           | 3/8 wells [00:00<00:00, 899.55wellCreating sequences | Current: WELL_0:  38%|██████▊           | 3/8 wells [00:00<00:00, 691.86wellCreating sequences | Current: WELL_0:  50%|█████████         | 4/8 wells [00:00<00:00, 922.48wellCreating sequences | Current: WELL_7:  50%|█████████         | 4/8 wells [00:00<00:00, 922.48wellCreating sequences | Current: WELL_7:  62%|███████████▎      | 5/8 wells [00:00<00:00, 866.91wellCreating sequences | Current: WELL_2:  62%|███████████▎      | 5/8 wells [00:00<00:00, 866.91wellCreating sequences | Current: WELL_2:  75%|█████████████▌    | 6/8 wells [00:00<00:00, 885.50wellCreating sequences | Current: WELL_9:  75%|█████████████▌    | 6/8 wells [00:00<00:00, 818.37wellCreating sequences | Current: WELL_9:  88%|███████████████▊  | 7/8 wells [00:00<00:00, 954.77wellCreating sequences | Current: WELL_4:  88%|███████████████▊  | 7/8 wells [00:00<00:00, 954.77wellCreating sequences | Current: WELL_4: 100%|██████████████████| 8/8 wells [00:00<00:00, 953.74wellCreating sequences | Current: WELL_4: 100%|██████████████████| 8/8 wells [00:00<00:00, 953.74well/s]

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 8
   • Total sequences created: 0
   🔄 Trying enhanced preprocessing with smaller sequence length: 8
Sequence creation mode: TRAINING
Creating sequences:   0%|                                             | 0/8 wells [00:00<?, ?wellCreating sequences | Current: WELL_8:   0%|                           | 0/8 wells [00:00<?, ?wellCreating sequences | Current: WELL_8:  12%|██▎               | 1/8 wells [00:00<00:00, 235.65wellCreating sequences | Current: WELL_1:  12%|██▎               | 1/8 wells [00:00<00:00, 235.65wellCreating sequences | Current: WELL_1:  25%|████▌             | 2/8 wells [00:00<00:00, 318.97wellCreating sequences | Current: WELL_5:  25%|████▌             | 2/8 wells [00:00<00:00, 318.97wellCreating sequences | Current: WELL_5:  38%|██████▊           | 3/8 wells [00:00<00:00, 414.05wellCreating sequences | Current: WELL_0:  38%|██████▊           | 3/8 wells [00:00<00:00, 363.84wellCreating sequences | Current: WELL_0:  50%|█████████         | 4/8 wells [00:00<00:00, 432.12wellCreating sequences | Current: WELL_7:  50%|█████████         | 4/8 wells [00:00<00:00, 432.12wellCreating sequences | Current: WELL_7:  62%|███████████▎      | 5/8 wells [00:00<00:00, 397.04wellCreating sequences | Current: WELL_2:  62%|███████████▎      | 5/8 wells [00:00<00:00, 397.04wellCreating sequences | Current: WELL_2:  75%|█████████████▌    | 6/8 wells [00:00<00:00, 441.91wellCreating sequences | Current: WELL_9:  75%|█████████████▌    | 6/8 wells [00:00<00:00, 411.64wellCreating sequences | Current: WELL_9:  88%|███████████████▊  | 7/8 wells [00:00<00:00, 453.89wellCreating sequences | Current: WELL_4:  88%|███████████████▊  | 7/8 wells [00:00<00:00, 453.89wellCreating sequences | Current: WELL_4: 100%|██████████████████| 8/8 wells [00:00<00:00, 458.53wellCreating sequences | Current: WELL_4: 100%|██████████████████| 8/8 wells [00:00<00:00, 445.26well/s]

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 8
   • Total sequences created: 16
   ✅ Enhanced preprocessing succeeded with sequence length 8
Using enhanced normalization with winsorization...
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: TRAINING
Creating sequences:   0%|                                             | 0/8 wells [00:00<?, ?wellCreating sequences | Current: WELL_8:   0%|                           | 0/8 wells [00:00<?, ?wellCreating sequences | Current: WELL_8:  12%|██▎               | 1/8 wells [00:00<00:00, 976.56wellCreating sequences | Current: WELL_1:  12%|██▎               | 1/8 wells [00:00<00:00, 976.56wellCreating sequences | Current: WELL_1:  25%|████▌             | 2/8 wells [00:00<00:00, 994.26wellCreating sequences | Current: WELL_5:  25%|████▌             | 2/8 wells [00:00<00:00, 994.26wellCreating sequences | Current: WELL_5:  38%|██████▊           | 3/8 wells [00:00<00:00, 882.70wellCreating sequences | Current: WELL_0:  38%|██████▊           | 3/8 wells [00:00<00:00, 856.56wellCreating sequences | Current: WELL_0:  50%|████████▌        | 4/8 wells [00:00<00:00, 1142.08wellCreating sequences | Current: WELL_7:  50%|█████████         | 4/8 wells [00:00<00:00, 880.37wellCreating sequences | Current: WELL_7:  62%|███████████▎      | 5/8 wells [00:00<00:00, 970.19wellCreating sequences | Current: WELL_2:  62%|███████████▎      | 5/8 wells [00:00<00:00, 970.19wellCreating sequences | Current: WELL_2:  75%|████████████▊    | 6/8 wells [00:00<00:00, 1029.53wellCreating sequences | Current: WELL_9:  75%|████████████▊    | 6/8 wells [00:00<00:00, 1029.53wellCreating sequences | Current: WELL_9:  88%|██████████████▉  | 7/8 wells [00:00<00:00, 1017.33wellCreating sequences | Current: WELL_4:  88%|██████████████▉  | 7/8 wells [00:00<00:00, 1017.33wellCreating sequences | Current: WELL_4: 100%|█████████████████| 8/8 wells [00:00<00:00, 1017.48wellCreating sequences | Current: WELL_4: 100%|█████████████████| 8/8 wells [00:00<00:00, 1017.48well/s]

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 8
   • Total sequences created: 0
Enhanced sequences shape: (0, 64, 6), type: <class 'numpy.ndarray'>, dtype: float32
⚠️ Enhanced sequence creation failed - falling back to standard method
   Enhanced result shape: (0, 64, 6) (expected: (n_sequences, 64, 6))
   Data quality check:
     - Total wells: 8
     - Total rows: 8
     - Feature columns: ['GR', 'NPHI', 'RHOB', 'RT', 'TVDSS', 'P-WAVE']
     - Sequence length: 64, step: 1
   🔄 Trying enhanced preprocessing with smaller sequence length: 32
Sequence creation mode: TRAINING
Creating sequences:   0%|                                             | 0/8 wells [00:00<?, ?wellCreating sequences | Current: WELL_8:   0%|                           | 0/8 wells [00:00<?, ?wellCreating sequences | Current: WELL_8:  12%|███▍                       | 1/8 wells [00:00<?, ?wellCreating sequences | Current: WELL_1:  12%|███▍                       | 1/8 wells [00:00<?, ?wellCreating sequences | Current: WELL_1:  25%|██████▊                    | 2/8 wells [00:00<?, ?wellCreating sequences | Current: WELL_5:  25%|██████▊                    | 2/8 wells [00:00<?, ?wellCreating sequences | Current: WELL_5:  38%|██████████▏                | 3/8 wells [00:00<?, ?wellCreating sequences | Current: WELL_0:  38%|██████████▏                | 3/8 wells [00:00<?, ?wellCreating sequences | Current: WELL_0:  50%|█████████████▌             | 4/8 wells [00:00<?, ?wellCreating sequences | Current: WELL_7:  50%|█████████████▌             | 4/8 wells [00:00<?, ?wellCreating sequences | Current: WELL_7:  62%|████████████████▉          | 5/8 wells [00:00<?, ?wellCreating sequences | Current: WELL_2:  62%|████████████████▉          | 5/8 wells [00:00<?, ?wellCreating sequences | Current: WELL_2:  75%|████████████▊    | 6/8 wells [00:00<00:00, 1261.44wellCreating sequences | Current: WELL_9:  75%|████████████▊    | 6/8 wells [00:00<00:00, 1261.44wellCreating sequences | Current: WELL_9:  88%|██████████████▉  | 7/8 wells [00:00<00:00, 1217.25wellCreating sequences | Current: WELL_4:  88%|██████████████▉  | 7/8 wells [00:00<00:00, 1037.13wellCreating sequences | Current: WELL_4: 100%|█████████████████| 8/8 wells [00:00<00:00, 1031.49wellCreating sequences | Current: WELL_4: 100%|█████████████████| 8/8 wells [00:00<00:00, 1031.49well/s]

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 8
   • Total sequences created: 0
   🔄 Trying enhanced preprocessing with smaller sequence length: 16
Sequence creation mode: TRAINING
Creating sequences:   0%|                                             | 0/8 wells [00:00<?, ?wellCreating sequences | Current: WELL_8:   0%|                           | 0/8 wells [00:00<?, ?wellCreating sequences | Current: WELL_8:  12%|██▏              | 1/8 wells [00:00<00:00, 1985.94wellCreating sequences | Current: WELL_1:  12%|██▏              | 1/8 wells [00:00<00:00, 1985.94wellCreating sequences | Current: WELL_1:  25%|████▎            | 2/8 wells [00:00<00:00, 1325.01wellCreating sequences | Current: WELL_5:  25%|████▌             | 2/8 wells [00:00<00:00, 798.23wellCreating sequences | Current: WELL_5:  38%|██████▊           | 3/8 wells [00:00<00:00, 940.99wellCreating sequences | Current: WELL_0:  38%|██████▊           | 3/8 wells [00:00<00:00, 940.99wellCreating sequences | Current: WELL_0:  50%|████████▌        | 4/8 wells [00:00<00:00, 1092.62wellCreating sequences | Current: WELL_7:  50%|████████▌        | 4/8 wells [00:00<00:00, 1092.62wellCreating sequences | Current: WELL_7:  62%|██████████▋      | 5/8 wells [00:00<00:00, 1060.56wellCreating sequences | Current: WELL_2:  62%|██████████▋      | 5/8 wells [00:00<00:00, 1060.56wellCreating sequences | Current: WELL_2:  75%|████████████▊    | 6/8 wells [00:00<00:00, 1056.99wellCreating sequences | Current: WELL_9:  75%|████████████▊    | 6/8 wells [00:00<00:00, 1056.99wellCreating sequences | Current: WELL_9:  88%|██████████████▉  | 7/8 wells [00:00<00:00, 1233.15wellCreating sequences | Current: WELL_4:  88%|██████████████▉  | 7/8 wells [00:00<00:00, 1233.15wellCreating sequences | Current: WELL_4: 100%|█████████████████| 8/8 wells [00:00<00:00, 1409.32wellCreating sequences | Current: WELL_4: 100%|█████████████████| 8/8 wells [00:00<00:00, 1409.32well/s]

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 8
   • Total sequences created: 0
   🔄 Trying enhanced preprocessing with smaller sequence length: 8
Sequence creation mode: TRAINING
Creating sequences:   0%|                                             | 0/8 wells [00:00<?, ?wellCreating sequences | Current: WELL_8:   0%|                           | 0/8 wells [00:00<?, ?wellCreating sequences | Current: WELL_8:  12%|██▏              | 1/8 wells [00:00<00:00, 1008.49wellCreating sequences | Current: WELL_1:  12%|██▏              | 1/8 wells [00:00<00:00, 1008.49wellCreating sequences | Current: WELL_1:  25%|████▎            | 2/8 wells [00:00<00:00, 1004.26wellCreating sequences | Current: WELL_5:  25%|████▎            | 2/8 wells [00:00<00:00, 1004.26wellCreating sequences | Current: WELL_5:  38%|██████▍          | 3/8 wells [00:00<00:00, 1000.55wellCreating sequences | Current: WELL_0:  38%|██████▍          | 3/8 wells [00:00<00:00, 1000.55wellCreating sequences | Current: WELL_0:  50%|████████▌        | 4/8 wells [00:00<00:00, 1003.48wellCreating sequences | Current: WELL_7:  50%|████████▌        | 4/8 wells [00:00<00:00, 1003.48wellCreating sequences | Current: WELL_7:  62%|██████████▋      | 5/8 wells [00:00<00:00, 1001.65wellCreating sequences | Current: WELL_2:  62%|██████████▋      | 5/8 wells [00:00<00:00, 1001.65wellCreating sequences | Current: WELL_2:  75%|████████████▊    | 6/8 wells [00:00<00:00, 1001.19wellCreating sequences | Current: WELL_9:  75%|████████████▊    | 6/8 wells [00:00<00:00, 1001.19wellCreating sequences | Current: WELL_9:  88%|██████████████▉  | 7/8 wells [00:00<00:00, 1001.51wellCreating sequences | Current: WELL_4:  88%|██████████████▉  | 7/8 wells [00:00<00:00, 1001.51wellCreating sequences | Current: WELL_4: 100%|█████████████████| 8/8 wells [00:00<00:00, 1001.51wellCreating sequences | Current: WELL_4: 100%|█████████████████| 8/8 wells [00:00<00:00, 1001.51well/s]

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 8
   • Total sequences created: 0
   ⚠️ Enhanced preprocessing failed with all sequence lengths - falling back to standard method
Creating basic sequences:   0%|                                       | 0/8 wells [00:00<?, ?wellCreating basic sequences | Current: WELL_0:   0%|                     | 0/8 wells [00:00<?, ?wellCreating basic sequences | Current: WELL_0:  12%|█▌          | 1/8 wells [00:00<00:00, 499.62wellCreating basic sequences | Current: WELL_1:  12%|█▌          | 1/8 wells [00:00<00:00, 499.62wellCreating basic sequences | Current: WELL_1:  25%|███         | 2/8 wells [00:00<00:00, 671.20wellCreating basic sequences | Current: WELL_2:  25%|███         | 2/8 wells [00:00<00:00, 671.20wellCreating basic sequences | Current: WELL_2:  38%|████▌       | 3/8 wells [00:00<00:00, 749.96wellCreating basic sequences | Current: WELL_4:  38%|████▌       | 3/8 wells [00:00<00:00, 600.19wellCreating basic sequences | Current: WELL_4:  50%|██████      | 4/8 wells [00:00<00:00, 800.25wellCreating basic sequences | Current: WELL_5:  50%|██████      | 4/8 wells [00:00<00:00, 658.14wellCreating basic sequences | Current: WELL_5:  62%|███████▌    | 5/8 wells [00:00<00:00, 716.46wellCreating basic sequences | Current: WELL_7:  62%|███████▌    | 5/8 wells [00:00<00:00, 716.46wellCreating basic sequences | Current: WELL_7:  75%|█████████   | 6/8 wells [00:00<00:00, 746.89wellCreating basic sequences | Current: WELL_8:  75%|█████████   | 6/8 wells [00:00<00:00, 668.29wellCreating basic sequences | Current: WELL_8:  88%|██████████▌ | 7/8 wells [00:00<00:00, 698.93wellCreating basic sequences | Current: WELL_9:  88%|██████████▌ | 7/8 wells [00:00<00:00, 698.93wellCreating basic sequences | Current: WELL_9: 100%|████████████| 8/8 wells [00:00<00:00, 727.18wellCreating basic sequences | Current: WELL_9: 100%|████████████| 8/8 wells [00:00<00:00, 727.18well/s]

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 8
   • Total sequences created: 0
Warning: No valid sequences could be created. Check data quality and sequence length.
📚 Introducing missingness for imputation training.
Using enhanced missing value introduction with realistic patterns...
Introduced 15.5% missing values (119 elements)
Pattern: 69 random + 50 chunked
Enhanced missing sequences shape: (16, 8, 6), type: <class 'numpy.ndarray'>, dtype: float64      
Using enhanced missing value introduction with realistic patterns...
❌ Optimized training failed: Input sequences must be 3D array (n_sequences, seq_len, n_features)
   Falling back to original function...
--- Running Deep Learning Model: SAITS (Self-Attention) ---
CuDNN benchmark enabled for performance.
Automatic well assignment:
  - Training/Validation wells: ['B-G-10', 'B-L-9', 'B-G-6', 'EB-1', 'B-L-1', 'B-L-6']
  - Test wells: ['B-L-2.G1', 'B-L-15']
   Using adaptive validation ratio: 30.0% (median well size: 7979)
   Minimum well size threshold: 12 (validation ratio: 30.0%)
Flexible Split Report:
  - Wells for Training/Validation: 6
  - Wells for Final Testing: 2
  - Train Samples (Shallow part of train wells): 32126
  - Validation Samples (Deeper part of train wells): 13772
  - Test Samples (Entirely separate wells): 11958

Running data leakage detection on splits...
🔍 Performing comprehensive data leakage check...
============================================================
🔍 Detecting perfect correlation leakage...
🚨 POTENTIAL DATA LEAKAGE DETECTED!
   TEST split:
     - TVDSS: correlation = 0.966
🕐 Validating temporal split integrity...
✅ No temporal leakage detected in splits
🎯 Detecting target leakage in features...
✅ No target leakage detected in features
============================================================
📊 COMPREHENSIVE LEAKAGE CHECK SUMMARY
============================================================
🚨 DATA LEAKAGE DETECTED!
   Failed checks: 1/3
   Data quality score: 0.67

📋 All Recommendations:
   ⚠️ Suspiciously high correlations detected (>0.95)
   • Check if target information is leaking into features
   • Verify temporal ordering of data splits
   • Review feature engineering process
   • Consider removing highly correlated features
   ✅ No temporal leakage detected in splits
   ✅ No target leakage detected in features
============================================================
⚠️ Data leakage detected but continuing with training...
   Review the recommendations above to improve data quality.

Using enhanced normalization with winsorization...
Normalized 'GR': method=standard, valid_data=31964/32126 (99.5%)
Normalized 'NPHI': method=standard, valid_data=24749/32126 (77.0%)
Normalized 'RHOB': method=standard, valid_data=20743/32126 (64.6%)
Normalized 'RT': method=standard, valid_data=31442/32126 (97.9%)
Normalized 'TVDSS': method=standard, valid_data=32126/32126 (100.0%)
Normalized 'P-WAVE': method=standard, valid_data=21526/32126 (67.0%)
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: TRAINING
Creating sequences:   0%|                                             | 0/6 wells [00:00<?, ?wellCreating sequences | Current: B-G-10:   0%|                           | 0/6 wells [00:00<?, ?wellCreating sequences | Current: B-G-10:  17%|███▏               | 1/6 wells [00:00<00:04,  1.12wellCreating sequences | Current: B-G-10:  17%|███▏               | 1/6 wells [00:00<00:04,  1.12wellCreating sequences | Current: B-L-9:  17%|███▎                | 1/6 wells [00:00<00:04,  1.12wellCreating sequences | Current: B-L-9:  33%|██████▋             | 2/6 wells [00:01<00:02,  1.53wellCreating sequences | Current: B-L-9:  33%|██████▋             | 2/6 wells [00:01<00:02,  1.53wellCreating sequences | Current: B-G-6:  33%|██████▋             | 2/6 wells [00:01<00:02,  1.53wellCreating sequences | Current: B-G-6:  50%|██████████          | 3/6 wells [00:02<00:02,  1.27wellCreating sequences | Current: B-G-6:  50%|██████████          | 3/6 wells [00:02<00:02,  1.27wellCreating sequences | Current: EB-1:  50%|██████████▌          | 3/6 wells [00:02<00:02,  1.27wellCreating sequences | Current: EB-1:  67%|██████████████       | 4/6 wells [00:03<00:01,  1.29wellCreating sequences | Current: EB-1:  67%|██████████████       | 4/6 wells [00:03<00:01,  1.29wellCreating sequences | Current: B-L-1:  67%|█████████████▎      | 4/6 wells [00:03<00:01,  1.29wellCreating sequences | Current: B-L-1:  83%|████████████████▋   | 5/6 wells [00:03<00:00,  1.40wellCreating sequences | Current: B-L-1:  83%|████████████████▋   | 5/6 wells [00:03<00:00,  1.40wellCreating sequences | Current: B-L-6:  83%|████████████████▋   | 5/6 wells [00:03<00:00,  1.40wellCreating sequences | Current: B-L-6: 100%|████████████████████| 6/6 wells [00:04<00:00,  1.69wellCreating sequences | Current: B-L-6: 100%|████████████████████| 6/6 wells [00:04<00:00,  1.69wellCreating sequences | Current: B-L-6: 100%|████████████████████| 6/6 wells [00:04<00:00,  1.48well/s]

📊 Well Processing Summary:
   • Total wells processed: 6
   • Successful: 6
   • Failed: 0
   • Total valid intervals: 15
   • Total sequences created: 18,052
Enhanced sequences shape: (18052, 64, 6), type: <class 'numpy.ndarray'>, dtype: float64
Using enhanced normalization with winsorization...
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: TRAINING
Creating sequences:   0%|                                             | 0/6 wells [00:00<?, ?wellCreating sequences | Current: B-G-10:   0%|                           | 0/6 wells [00:00<?, ?wellCreating sequences | Current: B-G-10:  17%|███▏               | 1/6 wells [00:00<00:00,  6.71wellCreating sequences | Current: B-G-10:  17%|███▏               | 1/6 wells [00:00<00:00,  6.71wellCreating sequences | Current: B-L-9:  17%|███▎                | 1/6 wells [00:00<00:00,  6.71wellCreating sequences | Current: B-L-9:  33%|██████▋             | 2/6 wells [00:00<00:01,  3.89wellCreating sequences | Current: B-L-9:  33%|██████▋             | 2/6 wells [00:00<00:01,  3.89wellCreating sequences | Current: B-G-6:  33%|██████▋             | 2/6 wells [00:00<00:01,  3.89wellCreating sequences | Current: B-G-6:  50%|██████████          | 3/6 wells [00:00<00:00,  5.19wellCreating sequences | Current: B-G-6:  50%|██████████          | 3/6 wells [00:00<00:00,  5.19wellCreating sequences | Current: EB-1:  50%|██████████▌          | 3/6 wells [00:00<00:00,  5.19wellCreating sequences | Current: EB-1:  67%|██████████████       | 4/6 wells [00:00<00:00,  4.24wellCreating sequences | Current: EB-1:  67%|██████████████       | 4/6 wells [00:00<00:00,  4.24wellCreating sequences | Current: B-L-1:  67%|█████████████▎      | 4/6 wells [00:00<00:00,  4.24wellCreating sequences | Current: B-L-1:  83%|████████████████▋   | 5/6 wells [00:01<00:00,  4.41wellCreating sequences | Current: B-L-1:  83%|████████████████▋   | 5/6 wells [00:01<00:00,  4.41wellCreating sequences | Current: B-L-6:  83%|████████████████▋   | 5/6 wells [00:01<00:00,  4.41wellCreating sequences | Current: B-L-6: 100%|████████████████████| 6/6 wells [00:01<00:00,  4.33wellCreating sequences | Current: B-L-6: 100%|████████████████████| 6/6 wells [00:01<00:00,  4.33wellCreating sequences | Current: B-L-6: 100%|████████████████████| 6/6 wells [00:01<00:00,  4.45well/s]

📊 Well Processing Summary:
   • Total wells processed: 6
   • Successful: 6
   • Failed: 0
   • Total valid intervals: 6
   • Total sequences created: 5,931
Enhanced sequences shape: (5931, 64, 6), type: <class 'numpy.ndarray'>, dtype: float64
📚 Introducing missingness for imputation training.
Using enhanced missing value introduction with realistic patterns...
Introduced 26.1% missing values (1806834 elements)
Pattern: 623877 random + 1182957 chunked
Enhanced missing sequences shape: (18052, 64, 6), type: <class 'numpy.ndarray'>, dtype: float64
Using enhanced missing value introduction with realistic patterns...
Introduced 26.0% missing values (592906 elements)
Pattern: 204975 random + 387931 chunked
Enhanced missing sequences shape: (5931, 64, 6), type: <class 'numpy.ndarray'>, dtype: float64
Converting sequences to tensors...
   Processing train_sequences_missing: current type is <class 'numpy.ndarray'>
   Changing train_sequences_missing dtype from float64 to float32.
   train_sequences_missing tensor created with shape: torch.Size([18052, 64, 6]), dtype: torch.float32
   Processing train_sequences_true: current type is <class 'numpy.ndarray'>
   Changing train_sequences_true dtype from float64 to float32.
   train_sequences_true tensor created with shape: torch.Size([18052, 64, 6]), dtype: torch.float32
   Processing val_sequences_missing: current type is <class 'numpy.ndarray'>
   Changing val_sequences_missing dtype from float64 to float32.
   val_sequences_missing tensor created with shape: torch.Size([5931, 64, 6]), dtype: torch.float32
   Processing val_sequences_true: current type is <class 'numpy.ndarray'>
   Changing val_sequences_true dtype from float64 to float32.
   val_sequences_true tensor created with shape: torch.Size([5931, 64, 6]), dtype: torch.float32 
   Tensor shapes - Train: torch.Size([18052, 64, 6]), Truth: torch.Size([18052, 64, 6])
🔧 Applied fixed parameters for SAITS (Self-Attention): {}
🔍 Pre-checking PyPOTS availability for SAITS (Self-Attention) model...
🔄 TensorFlow cache reset - will re-check availability
🔧 TensorFlow environment configured for compatibility (TF 2.15.1 available)
✅ TensorFlow 2.15.1 loaded (CPU-only)
✅ PyPOTS imported successfully with TensorFlow backend
✅ PyPOTS availability confirmed for SAITS (Self-Attention) model
Initialized SAITSModel with:
   - Features: 6
   - Sequence length: 64
   - Epochs: 40
   - Batch size: 64
   - Learning rate: 0.002

🔍 Performing comprehensive SAITS environment validation...

📋 Step 1: Environment Validation
✅ PyPOTS imported successfully with TensorFlow backend
   TensorFlow: available
   PyPOTS: available
   GPU: not_available

💡 Recommendations:
   💻 CPU-only mode - consider GPU setup
   ✅ PyPOTS ready for SAITS model

📋 Step 2: TensorFlow GPU Configuration
💻 No GPUs detected - using CPU mode
💻 CPU mode configured - consider GPU setup for better performance

💡 Performance Optimization Tips:
   💻 CPU-only mode detected
   💡 Consider installing CUDA-compatible TensorFlow for GPU acceleration
   💡 Use smaller batch sizes to avoid memory issues
   💡 Consider using distributed training for large datasets
✅ TensorFlow 2.15.1 ready

📋 Step 3: PyPOTS Import and Configuration
✅ PyPOTS imported successfully with TensorFlow backend
✅ PyPOTS components successfully imported: pypots=0.19, SAITS=SAITS

🎯 SAITS Model Ready for Training!
   💻 CPU mode - training will be slower but functional
🚀 Selected GPU 0: NVIDIA T550 Laptop GPU
   Memory: 4.3 GB
   Compute Capability: 7.5
ℹ️ TF32 available but not enabled (pre-Ampere GPU)
🔥 SDPA optimized backends enabled (Flash Attention support)
⚡ Mixed precision training available
✅ GPU initialized successfully
🚀 SAITS using GPU with fallback protection: cuda
✅ SAITS (Self-Attention) model created successfully
🔧 Forcing model initialization to prepare optimizer for schedulers...
🔧 Initializing SAITS model...
2025-08-20 15:15:28 [INFO]: Using the given device: cuda
2025-08-20 15:15:28 [WARNING]: ‼️ saving_path not given. Model files and tensorboard file will noot be saved.
2025-08-20 15:15:28 [INFO]: Using customized MAE as the training loss function.
2025-08-20 15:15:28 [INFO]: Using customized MSE as the validation metric function.
2025-08-20 15:15:28 [INFO]: SAITS initialized with the given hyperparameters, the number of trainable parameters: 3,165,152
✅ SAITS model initialized successfully
   - Parameters: ~674,816
   - Memory usage: ~10.6 MB
Training phase...
   About to train model with:
   - train_tensor type: <class 'torch.Tensor'>, shape: torch.Size([18052, 64, 6]), dtype: torch.float32
   - truth_tensor type: <class 'torch.Tensor'>, shape: torch.Size([18052, 64, 6]), dtype: torch.float32
🔧 Initializing training stability utilities...
INFO:preprocessing.deep_model.stability_preprocessing:🔧 Initialized gradient clipper for saits with max_norm=1.0
   ℹ️ Skipping AdaptiveLRScheduler for PyPOTS model (saits); optimizer is managed internally.    
   ✅ Gradient clipper (saits) initialized
Training PyPOTS-based model: SAITS (Self-Attention)
   Using tensor interface with batch_size=64, epochs=40
📊 Performance monitoring started
📊 Performance monitoring started for SAITS training
⚡ Mixed precision training enabled
🚀 Mixed precision training enabled via GPUManager (enhanced)
2025-08-20 15:15:55 [INFO]: Epoch 001 - training loss (MAE): 0.5700
2025-08-20 15:16:21 [INFO]: Epoch 002 - training loss (MAE): 0.4054
2025-08-20 15:16:49 [INFO]: Epoch 003 - training loss (MAE): 0.3865
2025-08-20 15:17:16 [INFO]: Epoch 004 - training loss (MAE): 0.3761
2025-08-20 15:17:44 [INFO]: Epoch 005 - training loss (MAE): 0.3693
2025-08-20 15:18:11 [INFO]: Epoch 006 - training loss (MAE): 0.3646
2025-08-20 15:18:42 [INFO]: Epoch 007 - training loss (MAE): 0.3604
2025-08-20 15:19:10 [INFO]: Epoch 008 - training loss (MAE): 0.3606
2025-08-20 15:19:38 [INFO]: Epoch 009 - training loss (MAE): 0.3576
2025-08-20 15:20:06 [INFO]: Epoch 010 - training loss (MAE): 0.3581
2025-08-20 15:20:35 [INFO]: Epoch 011 - training loss (MAE): 0.3540
2025-08-20 15:21:03 [INFO]: Epoch 012 - training loss (MAE): 0.3522
2025-08-20 15:21:32 [INFO]: Epoch 013 - training loss (MAE): 0.3506
2025-08-20 15:22:01 [INFO]: Epoch 014 - training loss (MAE): 0.3502
2025-08-20 15:22:29 [INFO]: Epoch 015 - training loss (MAE): 0.3544
2025-08-20 15:22:58 [INFO]: Epoch 016 - training loss (MAE): 0.3484
2025-08-20 15:23:27 [INFO]: Epoch 017 - training loss (MAE): 0.3446
2025-08-20 15:23:54 [INFO]: Epoch 018 - training loss (MAE): 0.3438
2025-08-20 15:24:21 [INFO]: Epoch 019 - training loss (MAE): 0.3448
2025-08-20 15:24:47 [INFO]: Epoch 020 - training loss (MAE): 0.3436
2025-08-20 15:25:13 [INFO]: Epoch 021 - training loss (MAE): 0.3431
2025-08-20 15:25:39 [INFO]: Epoch 022 - training loss (MAE): 0.3425
2025-08-20 15:26:06 [INFO]: Epoch 023 - training loss (MAE): 0.3426
2025-08-20 15:26:33 [INFO]: Epoch 024 - training loss (MAE): 0.3423
2025-08-20 15:27:02 [INFO]: Epoch 025 - training loss (MAE): 0.3452
2025-08-20 15:27:34 [INFO]: Epoch 026 - training loss (MAE): 0.3420
2025-08-20 15:28:06 [INFO]: Epoch 027 - training loss (MAE): 0.3409
2025-08-20 15:28:35 [INFO]: Epoch 028 - training loss (MAE): 0.3423
2025-08-20 15:29:03 [INFO]: Epoch 029 - training loss (MAE): 0.3387
2025-08-20 15:29:32 [INFO]: Epoch 030 - training loss (MAE): 0.3386
2025-08-20 15:30:01 [INFO]: Epoch 031 - training loss (MAE): 0.3377
2025-08-20 15:30:29 [INFO]: Epoch 032 - training loss (MAE): 0.3372
2025-08-20 15:30:56 [INFO]: Epoch 033 - training loss (MAE): 0.3428
2025-08-20 15:31:26 [INFO]: Epoch 034 - training loss (MAE): 0.3386
2025-08-20 15:31:54 [INFO]: Epoch 035 - training loss (MAE): 0.3378
2025-08-20 15:32:23 [INFO]: Epoch 036 - training loss (MAE): 0.3364
2025-08-20 15:32:51 [INFO]: Epoch 037 - training loss (MAE): 0.3369
2025-08-20 15:33:18 [INFO]: Epoch 038 - training loss (MAE): 0.3384
2025-08-20 15:33:48 [INFO]: Epoch 039 - training loss (MAE): 0.3360
2025-08-20 15:34:14 [INFO]: Epoch 040 - training loss (MAE): 0.3361
2025-08-20 15:34:14 [INFO]: Finished training. The best model is from epoch#39.
   ⏱️ Epoch time: 1125.86s, Memory Δ: +67.8MB
📊 Performance monitoring stopped

📊 SAITS Training Performance Summary:
   • Total Training Time: 1125.9s
Enhanced Evaluation Phase...
🧠 Large dataset detected (5,931 samples), using memory optimization

==================================================
[MEM] MEMORY STATUS
==================================================
System Memory:
   * Total: 31.7 GB
   * Available: 13.8 GB
   * Usage: 56.6%

GPU Memory:
   * Total: 4.0 GB
   * Allocated: 0.1 GB
   * Reserved: 0.1 GB
   * Free: 3.9 GB
==================================================
🧠 Memory-optimized prediction for 5,931 samples
[MEM] Calculated optimal batch size: 32
   * Available memory: 3215.1 MB
   * Estimated usage: 38.9 MB
   • Using batch size: 32
SAITS Prediction: 100%|████████████████████████████████████| 186/186 [01:26<00:00,  2.14batch/s] 
✅ SAITS Prediction: 100% [██████████████████████████] Done
✅ Batch alignment verified: 5931 predictions for 5931 input samples

📊 Memory status after processing:

==================================================
[MEM] MEMORY STATUS
==================================================
System Memory:
   * Total: 31.7 GB
   * Available: 14.0 GB
   * Usage: 55.9%

GPU Memory:
   * Total: 4.0 GB
   * Allocated: 0.1 GB
   * Reserved: 0.1 GB
   * Free: 3.9 GB
==================================================
Imputation Metrics (Artificial Missing Values):
   • MAE: 0.3016
   • R²: 0.4154
   • RMSE: 0.4206
   • Evaluated Points: 99368
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: PREDICTION
  - Using feature columns only for valid interval detection
  - Target column will be included in sequences as NaN for model prediction
Creating sequences:   0%|                                             | 0/6 wells [00:00<?, ?wellCreating sequences | Current: B-G-10:   0%|                           | 0/6 wells [00:00<?, ?wellCreating sequences | Current: B-G-10:  17%|███▏               | 1/6 wells [00:00<00:00,  6.23wellCreating sequences | Current: B-G-10:  17%|███▏               | 1/6 wells [00:00<00:00,  6.23wellCreating sequences | Current: B-L-9:  17%|███▎                | 1/6 wells [00:00<00:00,  6.23wellCreating sequences | Current: B-L-9:  33%|██████▋             | 2/6 wells [00:00<00:01,  3.56wellCreating sequences | Current: B-L-9:  33%|██████▋             | 2/6 wells [00:00<00:01,  3.56wellCreating sequences | Current: B-G-6:  33%|██████▋             | 2/6 wells [00:00<00:01,  3.56wellCreating sequences | Current: B-G-6:  50%|██████████          | 3/6 wells [00:00<00:00,  4.73wellCreating sequences | Current: B-G-6:  50%|██████████          | 3/6 wells [00:00<00:00,  4.73wellCreating sequences | Current: EB-1:  50%|██████████▌          | 3/6 wells [00:00<00:00,  4.73wellCreating sequences | Current: EB-1:  67%|██████████████       | 4/6 wells [00:00<00:00,  3.84wellCreating sequences | Current: EB-1:  67%|██████████████       | 4/6 wells [00:00<00:00,  3.84wellCreating sequences | Current: B-L-1:  67%|█████████████▎      | 4/6 wells [00:00<00:00,  3.84wellCreating sequences | Current: B-L-1:  83%|████████████████▋   | 5/6 wells [00:01<00:00,  4.06wellCreating sequences | Current: B-L-1:  83%|████████████████▋   | 5/6 wells [00:01<00:00,  4.06wellCreating sequences | Current: B-L-6:  83%|████████████████▋   | 5/6 wells [00:01<00:00,  4.06wellCreating sequences | Current: B-L-6: 100%|████████████████████| 6/6 wells [00:01<00:00,  3.90wellCreating sequences | Current: B-L-6: 100%|████████████████████| 6/6 wells [00:01<00:00,  3.90wellCreating sequences | Current: B-L-6: 100%|████████████████████| 6/6 wells [00:01<00:00,  4.03well/s]

📊 Well Processing Summary:
   • Total wells processed: 6
   • Successful: 6
   • Failed: 0
   • Total valid intervals: 6
   • Total sequences created: 5,942
Enhanced sequences shape: (5942, 64, 6), type: <class 'numpy.ndarray'>, dtype: float64
🧠 Large dataset detected (5,942 samples), using memory optimization

==================================================
[MEM] MEMORY STATUS
==================================================
System Memory:
   * Total: 31.7 GB
   * Available: 13.9 GB
   * Usage: 56.1%

GPU Memory:
   * Total: 4.0 GB
   * Allocated: 0.1 GB
   * Reserved: 0.1 GB
   * Free: 3.9 GB
==================================================
🧠 Memory-optimized prediction for 5,942 samples
[MEM] Calculated optimal batch size: 32
   * Available memory: 3215.1 MB
   * Estimated usage: 38.9 MB
   • Using batch size: 32
SAITS Prediction: 100%|████████████████████████████████████| 186/186 [01:21<00:00,  2.27batch/s] 
✅ SAITS Prediction: 100% [██████████████████████████] Done
✅ Batch alignment verified: 5942 predictions for 5942 input samples

📊 Memory status after processing:

==================================================
[MEM] MEMORY STATUS
==================================================
System Memory:
   * Total: 31.7 GB
   * Available: 13.8 GB
   * Usage: 56.4%

GPU Memory:
   * Total: 4.0 GB
   * Allocated: 0.1 GB
   * Reserved: 0.1 GB
   * Free: 3.9 GB
==================================================
⚠️ Optimization failed after 1316.79s: operands could not be broadcast together with shapes (3795584,) (380288,)
   🔄 Falling back to original implementation...
🚀 Starting Phase 1 Enhanced Deep Learning Training...
   Model: SAITS (Self-Attention)
   Target: P-WAVE
   Features: ['GR', 'NPHI', 'RHOB', 'RT', 'TVDSS']

📊 Step 1: Initial Data Preparation...
Using enhanced normalization with winsorization...
Normalized 'GR': method=standard, valid_data=51983/57856 (89.8%)
Normalized 'NPHI': method=standard, valid_data=42700/57856 (73.8%)
Normalized 'RHOB': method=standard, valid_data=36835/57856 (63.7%)
Normalized 'RT': method=standard, valid_data=47696/57856 (82.4%)
Normalized 'TVDSS': method=standard, valid_data=55307/57856 (95.6%)
Normalized 'P-WAVE': method=standard, valid_data=37801/57856 (65.3%)
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: TRAINING
Creating sequences:   0%|                                             | 0/8 wells [00:00<?, ?wellCreating sequences | Current: B-G-6:   0%|                            | 0/8 wells [00:00<?, ?wellCreating sequences | Current: B-G-6:  12%|██▌                 | 1/8 wells [00:01<00:08,  1.16s/weCreating sequences | Current: B-G-6:  12%|██▌                 | 1/8 wells [00:01<00:08,  1.16s/weCreating sequences | Current: B-G-10:  12%|██▍                | 1/8 wells [00:01<00:08,  1.16s/weCreating sequences | Current: B-G-10:  25%|████▊              | 2/8 wells [00:02<00:07,  1.17s/weCreating sequences | Current: B-G-10:  25%|████▊              | 2/8 wells [00:02<00:07,  1.17s/weCreating sequences | Current: B-L-1:  25%|█████               | 2/8 wells [00:02<00:07,  1.17s/weCreating sequences | Current: B-L-1:  38%|███████▌            | 3/8 wells [00:03<00:05,  1.06s/weCreating sequences | Current: B-L-1:  38%|███████▌            | 3/8 wells [00:03<00:05,  1.06s/weCreating sequences | Current: B-L-2.G1:  38%|██████▍          | 3/8 wells [00:03<00:05,  1.06s/weCreating sequences | Current: B-L-2.G1:  50%|████████▌        | 4/8 wells [00:04<00:04,  1.18s/weCreating sequences | Current: B-L-2.G1:  50%|████████▌        | 4/8 wells [00:04<00:04,  1.18s/weCreating sequences | Current: B-L-6:  50%|██████████          | 4/8 wells [00:04<00:04,  1.18s/weCreating sequences | Current: B-L-6:  62%|████████████▌       | 5/8 wells [00:05<00:02,  1.00wellCreating sequences | Current: B-L-6:  62%|████████████▌       | 5/8 wells [00:05<00:02,  1.00wellCreating sequences | Current: B-L-9:  62%|████████████▌       | 5/8 wells [00:05<00:02,  1.00wellCreating sequences | Current: B-L-9:  75%|███████████████     | 6/8 wells [00:06<00:01,  1.04wellCreating sequences | Current: B-L-9:  75%|███████████████     | 6/8 wells [00:06<00:01,  1.04wellCreating sequences | Current: B-L-15:  75%|██████████████▎    | 6/8 wells [00:06<00:01,  1.04wellCreating sequences | Current: B-L-15:  88%|████████████████▋  | 7/8 wells [00:07<00:00,  1.04wellCreating sequences | Current: B-L-15:  88%|████████████████▋  | 7/8 wells [00:07<00:00,  1.04wellCreating sequences | Current: EB-1:  88%|██████████████████▍  | 7/8 wells [00:07<00:00,  1.04wellCreating sequences | Current: EB-1: 100%|█████████████████████| 8/8 wells [00:08<00:00,  1.05s/weCreating sequences | Current: EB-1: 100%|█████████████████████| 8/8 wells [00:08<00:00,  1.05s/weCreating sequences | Current: EB-1: 100%|█████████████████████| 8/8 wells [00:08<00:00,  1.05s/well]

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 23
   • Total sequences created: 33,543
Enhanced sequences shape: (33543, 64, 6), type: <class 'numpy.ndarray'>, dtype: float64
   Created 33543 sequences
   Sequence shape: (33543, 64, 6)

🔍 Step 2: Phase 1 Advanced Preprocessing...
   Dataset characteristics:
     • Sequences: 33543
     • Missing rate: 0.0%
     • Recommended config: {'normalization_method': 'quantile', 'missing_encoding_method': 'learnable_embedding', 'validate_ranges': True}
INFO:preprocessing.deep_model.stability_preprocessing:🚀 Starting Phase 1 Advanced Preprocessing Pipeline...
INFO:preprocessing.deep_model.stability_preprocessing:
🔍 Step 1: Advanced Input Validation & Cleaning...
INFO:preprocessing.deep_model.stability_preprocessing:
❓ Step 2: Missing Value Encoding (learnable_embedding)...
INFO:preprocessing.deep_model.stability_preprocessing:
📊 Step 3: Robust Normalization (quantile)...
INFO:preprocessing.deep_model.stability_preprocessing:
🔧 Step 4: Final Numerical Stability Check...
INFO:preprocessing.deep_model.stability_preprocessing:✅ Final sequences pass all stability checks
INFO:preprocessing.deep_model.stability_preprocessing:
🎉 Phase 1 preprocessing pipeline completed successfully!
INFO:preprocessing.deep_model.stability_preprocessing:   Input shape: (33543, 64, 6) → Output shape: (33543, 64, 6)
INFO:preprocessing.deep_model.stability_preprocessing:   Data quality score: 0.950
INFO:preprocessing.deep_model.stability_preprocessing:   Ready for stable deep learning training!
✅ Phase 1 preprocessing completed:
   • Data quality score: 0.950
   • Missing rate: 0.0% → 0.0% (default)
   • Final stability: ✅ STABLE

🎯 Step 3: Preparing Training Sequences...
   📚 IMPUTATION MODE: Creating training sequences with missing values
Using enhanced missing value introduction with realistic patterns...
Introduced 26.0% missing values (3354999 elements)
Pattern: 1159245 random + 2195754 chunked
Enhanced missing sequences shape: (33543, 64, 6), type: <class 'numpy.ndarray'>, dtype: float64
   Training sequences with missing values: (33543, 64, 6)

🔧 Step 3.5: Encoding artificial missing values for imputation training...
   ✅ Final training sequences encoded. Missing values are now represented as tokens.
❌ Fallback also failed: 'missing_count_before'
[MEM] Memory cleared
saits failed with error: Both optimized and fallback implementations failed. Optimization error: operands could not be broadcast together with shapes (379584,) (380288,) , Fallback error: 'missing_count_before'

Batch execution summary:
   • Successful models: 0
   • Failed models: 1
   • Failed: saits
All models failed. Continuing to next step...

Step 10: Configure output options
No successful models to process. Skipping to next step...