#!/usr/bin/env python3
"""
Test script to validate SAITS error fix implementation.
This script tests the enhanced PyPOTS availability check and GPU detection.
"""

import sys
import os
import traceback
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_tensorflow_compatibility():
    """
    Test the enhanced tensorflow_compatibility module functions.
    """
    print("\n" + "="*60)
    print("🧪 TESTING TENSORFLOW COMPATIBILITY MODULE")
    print("="*60)
    
    try:
        from utils import tensorflow_compatibility
        
        # Test 1: Basic TensorFlow import
        print("\n📋 Test 1: TensorFlow Import")
        tf_success, tf_module, tf_error = tensorflow_compatibility.import_tensorflow_safe(force_recheck=True)
        if tf_success:
            print(f"✅ TensorFlow import successful: {tensorflow_compatibility.TENSORFLOW_VERSION}")
            print(f"   GPU Available: {tensorflow_compatibility.TENSORFLOW_GPU_AVAILABLE}")
        else:
            print(f"❌ TensorFlow import failed: {tf_error}")
        
        # Test 2: GPU Configuration
        print("\n📋 Test 2: GPU Configuration")
        if tf_success and tf_module:
            gpu_configured = tensorflow_compatibility.configure_tensorflow_gpu_memory(tf_module)
            print(f"   GPU Configuration Result: {gpu_configured}")
        else:
            print("   Skipped - TensorFlow not available")
        
        # Test 3: GPU Optimization Recommendations
        print("\n📋 Test 3: GPU Optimization Recommendations")
        recommendations = tensorflow_compatibility.get_gpu_optimization_recommendations()
        print(f"   GPU Available: {recommendations['gpu_available']}")
        print(f"   TensorFlow Version: {recommendations['tensorflow_version']}")
        print("   Recommendations:")
        for rec in recommendations['recommendations']:
            print(f"     {rec}")
        
        # Test 4: Environment Validation
        print("\n📋 Test 4: Environment Validation")
        validation_results = tensorflow_compatibility.validate_pypots_environment()
        print(f"   TensorFlow Status: {validation_results['tensorflow_status']}")
        print(f"   PyPOTS Status: {validation_results['pypots_status']}")
        print(f"   GPU Status: {validation_results['gpu_status']}")
        
        if validation_results['errors']:
            print("   Errors:")
            for error in validation_results['errors']:
                print(f"     - {error}")
        
        if validation_results['recommendations']:
            print("   Recommendations:")
            for rec in validation_results['recommendations']:
                print(f"     {rec}")
        
        return True
        
    except Exception as e:
        print(f"❌ TensorFlow compatibility test failed: {str(e)}")
        traceback.print_exc()
        return False

def test_saits_model_initialization():
    """
    Test the enhanced SAITS model initialization and dependency checking.
    """
    print("\n" + "="*60)
    print("🧪 TESTING SAITS MODEL INITIALIZATION")
    print("="*60)
    
    try:
        # Import SAITS model
        from models.advanced_models.saits_model import SAITSModel
        
        print("\n📋 Test 1: SAITS Model Import")
        print("✅ SAITSModel class imported successfully")
        
        print("\n📋 Test 2: SAITS Model Initialization")
        # Create a minimal configuration for testing
        test_config = {
            'n_steps': 24,
            'n_features': 5,
            'd_model': 64,
            'n_heads': 4,
            'd_k': 16,
            'd_v': 16,
            'd_ffn': 128,
            'n_layers': 2,
            'dropout': 0.1,
            'epochs': 1,  # Minimal for testing
            'batch_size': 32,
            'patience': 5,
            'device': 'auto'
        }
        
        # Initialize SAITS model (this will trigger _check_pypots_availability)
        saits_model = SAITSModel(test_config)
        print("✅ SAITS model initialized successfully")
        
        # Test the dependency check method directly
        print("\n📋 Test 3: Direct Dependency Check")
        dependency_check_result = saits_model._check_pypots_availability()
        print(f"   Dependency Check Result: {dependency_check_result}")
        
        return True
        
    except Exception as e:
        print(f"❌ SAITS model test failed: {str(e)}")
        traceback.print_exc()
        return False

def test_pypots_import():
    """
    Test PyPOTS import functionality.
    """
    print("\n" + "="*60)
    print("🧪 TESTING PYPOTS IMPORT")
    print("="*60)
    
    try:
        from utils import tensorflow_compatibility
        
        print("\n📋 Test 1: PyPOTS Safe Import")
        success, modules, error = tensorflow_compatibility.import_pypots_safe(force_recheck=True)
        
        if success and modules:
            print("✅ PyPOTS import successful")
            print(f"   Available modules: {list(modules.keys())}")
            
            # Test specific components
            if 'SAITS' in modules:
                print(f"   SAITS class: {modules['SAITS']}")
            if 'BaseNNImputer' in modules:
                print(f"   BaseNNImputer class: {modules['BaseNNImputer']}")
            if 'pypots' in modules:
                pypots_version = getattr(modules['pypots'], '__version__', 'unknown')
                print(f"   PyPOTS version: {pypots_version}")
        else:
            print(f"❌ PyPOTS import failed: {error}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ PyPOTS import test failed: {str(e)}")
        traceback.print_exc()
        return False

def main():
    """
    Main test function to run all SAITS fix validation tests.
    """
    print("🚀 SAITS ERROR FIX VALIDATION TESTS")
    print("="*60)
    print("Testing the implementation of Solution 1 from the SAITS error fix proposal.")
    print("This validates GPU detection, enhanced error handling, and PyPOTS integration.")
    
    test_results = []
    
    # Run all tests
    test_results.append(("TensorFlow Compatibility", test_tensorflow_compatibility()))
    test_results.append(("PyPOTS Import", test_pypots_import()))
    test_results.append(("SAITS Model Initialization", test_saits_model_initialization()))
    
    # Summary
    print("\n" + "="*60)
    print("🏁 TEST RESULTS SUMMARY")
    print("="*60)
    
    passed_tests = 0
    total_tests = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {test_name}: {status}")
        if result:
            passed_tests += 1
    
    print(f"\n📊 Overall Result: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 All tests passed! SAITS error fix implementation is successful.")
        print("\n💡 Next Steps:")
        print("   1. The SAITS model should now work without TensorFlow import errors")
        print("   2. GPU acceleration is properly configured if available")
        print("   3. Enhanced error messages provide better troubleshooting guidance")
        return True
    else:
        print("⚠️ Some tests failed. Please review the error messages above.")
        print("\n🔧 Troubleshooting:")
        print("   1. Ensure all dependencies are installed: pip install -r requirements.txt")
        print("   2. Check TensorFlow installation: python -c 'import tensorflow; print(tensorflow.__version__)'")
        print("   3. Verify PyPOTS installation: python -c 'import pypots; print(pypots.__version__)'")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error during testing: {str(e)}")
        traceback.print_exc()
        sys.exit(1)