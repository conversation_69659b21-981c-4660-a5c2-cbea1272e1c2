#!/usr/bin/env python3
"""
Simple SAITS Error Fix Validation Test
Tests the implementation without TensorFlow re-initialization issues.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("🚀 SIMPLE SAITS ERROR FIX VALIDATION")
print("=" * 60)
print("Testing SAITS model initialization and PyPOTS integration.")
print("=" * 60)

try:
    # Test 1: Import tensorflow_compatibility module
    print("\n📋 Test 1: TensorFlow Compatibility Module")
    from utils.tensorflow_compatibility import (
        import_tensorflow_safe,
        import_pypots_safe,
        get_tensorflow_status,
        configure_tensorflow_gpu_memory,
        get_gpu_optimization_recommendations,
        validate_pypots_environment
    )
    print("✅ TensorFlow compatibility module imported successfully")
    
    # Test 2: Check TensorFlow status
    print("\n📋 Test 2: TensorFlow Status")
    tf_status = get_tensorflow_status()
    print(f"   TensorFlow Available: {tf_status['tensorflow_available']}")
    print(f"   Version: {tf_status['tensorflow_version']}")
    print(f"   GPU Available: {tf_status['tensorflow_gpu_available']}")
    
    # Test 3: GPU optimization recommendations
    print("\n📋 Test 3: GPU Optimization Recommendations")
    recommendations = get_gpu_optimization_recommendations()
    print(f"   GPU Available: {recommendations['gpu_available']}")
    print(f"   TensorFlow Version: {recommendations['tensorflow_version']}")
    print("   Recommendations:")
    for rec in recommendations['recommendations']:
        print(f"     {rec}")
    
    # Test 4: Environment validation
    print("\n📋 Test 4: PyPOTS Environment Validation")
    env_status = validate_pypots_environment()
    print(f"   TensorFlow Status: {env_status['tensorflow_status']}")
    print(f"   PyPOTS Status: {env_status['pypots_status']}")
    print(f"   GPU Status: {env_status['gpu_status']}")
    if env_status['errors']:
        print("   Errors found:")
        for error in env_status['errors']:
            print(f"     ❌ {error}")
    if env_status['recommendations']:
        print("   Recommendations:")
        for rec in env_status['recommendations']:
            print(f"     {rec}")
    
    # Test 5: SAITS model import and initialization
    print("\n📋 Test 5: SAITS Model Import")
    from models.advanced_models.saits_model import SAITSModel
    print("✅ SAITS model imported successfully")
    
    # Test 6: SAITS model dependency check
    print("\n📋 Test 6: SAITS Model Dependency Check")
    model = SAITSModel()
    print("✅ SAITS model instance created successfully")
    
    # Test the enhanced _check_pypots_availability method
    print("\n📋 Test 7: Enhanced PyPOTS Availability Check")
    model._check_pypots_availability()
    print("✅ PyPOTS availability check completed")
    
    print("\n🎉 ALL TESTS PASSED!")
    print("✅ SAITS error fix implementation is working correctly")
    print("✅ Enhanced GPU detection and error handling are functional")
    print("✅ PyPOTS integration is working properly")
    
except ImportError as e:
    print(f"❌ Import Error: {e}")
    print("💡 This indicates missing dependencies or import issues")
    sys.exit(1)
except Exception as e:
    print(f"❌ Unexpected Error: {e}")
    print(f"   Error Type: {type(e).__name__}")
    import traceback
    print("   Full traceback:")
    traceback.print_exc()
    sys.exit(1)

print("\n" + "=" * 60)
print("🏁 SAITS ERROR FIX VALIDATION COMPLETED SUCCESSFULLY")
print("=" * 60)