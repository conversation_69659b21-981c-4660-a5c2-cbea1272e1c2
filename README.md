# ML Log Prediction - Advanced Machine Learning Pipeline for Well Log Data

A comprehensive machine learning pipeline for predicting and imputing missing well log data using multiple advanced algorithms including gradient boosting models, deep learning approaches, and traditional statistical methods. This project features GPU acceleration, advanced preprocessing, and comprehensive visualization capabilities.

## 🎯 Project Overview

**Current Status**: Active development with comprehensive ML pipeline for well log prediction and GPU acceleration support.

This project provides a complete workflow for:
- **Loading and processing LAS (Log ASCII Standard) files**
- **Advanced data cleaning and quality control**
- **Multi-model machine learning prediction and imputation**
- **Comprehensive performance evaluation and visualization**
- **GPU-accelerated training for improved performance**
- **Professional reporting and output generation**

## 🏗️ Architecture

### Core Components
- **`main.py`**: Entry point with interactive GUI workflow orchestrating the complete ML pipeline
- **`core_code/data_handler.py`**: LAS file operations, data loading, cleaning, and preprocessing
- **`core_code/ml_core.py`**: Machine learning model registry and training pipeline with MODEL_REGISTRY
- **`config_handler.py`**: User interfaces, file selection dialogs, and configuration management
- **`reporting.py`**: Visualization, analysis, and performance reporting
- **`ml_core_phase1_integration.py`**: Enhanced preprocessing integration for advanced models
- **`core_code/data_leakage_detector.py`**: Data validation and leakage detection utilities
- **`mlr_utils.py`**: Multiple linear regression utilities with diagnostics

### Model Registry Architecture
The `MODEL_REGISTRY` in `ml_core.py` contains all available models with proper configuration including:
- **type**: 'shallow' (gradient boosting, statistical), 'deep' (basic neural networks), or 'deep_advanced' (SAITS, BRITS, etc.)
- **model_class**: Reference to the model implementation class
- **requires_sequences**: Boolean indicating if the model needs sequence data
- **config**: Model-specific hyperparameters and settings

Example registry entry:
```python
'xgboost': {
    'type': 'shallow',
    'model_class': XGBRegressor,
    'requires_sequences': False,
    'config': {'device': 'cuda', 'tree_method': 'gpu_hist'}
}
```

### Model Categories

#### Gradient Boosting Models (GPU-Accelerated)
- **XGBoost**: Modern GPU acceleration with `device='cuda'`
- **LightGBM**: High-performance gradient boosting
- **CatBoost**: Categorical feature handling with GPU support

#### Deep Learning Models
- **Advanced Models**: SAITS, BRITS implementations in `models/advanced_models/`
- **Neural Networks**: Basic neural network implementations in `models/neuralnet.py`
- **Enhanced Preprocessing**: Deep learning preprocessing pipelines in `preprocessing/deep_model/`

#### Statistical Models
- **Linear Regression**: Interpretable baseline with diagnostics
- **Ridge Regression**: L2 regularization for multicollinearity

## 🚀 Key Features

### Advanced Data Processing
- **Automated LAS file loading** with error handling
- **Smart data cleaning** with domain-specific rules
- **Enhanced preprocessing** with outlier detection
- **Sequence creation** for deep learning models
- **Data leakage detection** for model validation

### GPU Acceleration & Memory Optimization (Phase 1 - Completed ✅)
- **XGBoost GPU optimization** with modern CUDA support
- **Automatic fallback** to CPU when GPU unavailable
- **Environment Configuration**: `PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True` set before PyTorch imports
- **Memory Monitoring**: Real-time memory usage tracking via `utils.memory_optimization`
- **Mixed Precision Training**: Automatic AMP (Automatic Mixed Precision) for deep learning models
- **GPU Fallback**: Graceful fallback to CPU when GPU memory is insufficient
- **Adaptive Batch Processing**: Memory-efficient batch processing for large datasets (27,618+ samples)
- **Emergency OOM Recovery**: Automatic out-of-memory recovery with batch size reduction
- **Memory-Efficient Prediction**: Enhanced transformer prediction with progressive batch processing
- **Performance monitoring** and benchmarking

### Multi-Model Comparison
- **Batch model execution** with comprehensive comparison
- **Automated model ranking** based on composite scores
- **Side-by-side visualization** of model performance
- **Statistical evaluation** with multiple metrics (MAE, R², RMSE)

### Professional Visualization
- **Quality control plots** with cross-plot analysis
- **Model performance dashboards**
- **Residual analysis and diagnostics**
- **Publication-ready charts** with customizable styling

## 📋 Requirements

### System Requirements
- **Python 3.8+**
- **NVIDIA GPU** (optional, for GPU acceleration)
- **CUDA Toolkit** (for GPU support)

### Dependencies
See `requirements.txt` for complete list:
```
# Core Data Science
pandas>=1.3.0
numpy>=1.20.0
scikit-learn>=1.0.0
xgboost>=1.7.0
lightgbm>=3.3.0
catboost>=1.0.0

# Deep Learning
torch>=1.12.0
pypots>=0.5.0

# Data Handling
lasio>=0.29.0

# Visualization
matplotlib>=3.5.0
plotly>=5.0.0
seaborn>=0.11.0

# GUI
tkinter
```

## 🎛️ Usage

1. **Run the main application**:
   ```bash
   python main.py
   ```

2. **Follow the interactive workflow**:
   - Select input LAS files using the GUI dialog
   - Configure feature logs and target log
   - Choose training/prediction strategy
   - Select models to run (single or batch)
   - Review results and generate reports

3. **Output Options**:
   - Save results to LAS files
   - Generate detailed performance reports
   - Create comparison visualizations
   - Export quality control plots

## 🧪 Testing

The project includes comprehensive testing capabilities:

### Debug and Testing Tools
Testing and debugging tools are organized in the `archives/` directory:

```bash
# Debug and diagnostics scripts
python archives/debug/saits_error_diagnostics_and_fixes.py
python archives/debug/debug_enhanced_preprocessing.py
python archives/debug/test_pipeline_optimization.py

# Test scripts for model execution
python archives/test_scripts/test_saits_execution.py
python archives/test_scripts/test_option_a_reconstruction.py
```

### Running Tests
While there's no formal pytest framework installed, you can run individual debug and test files:

```bash
# Debug SAITS/BRITS model issues
python archives/debug/saits_error_diagnostics_and_fixes.py

# Test preprocessing pipeline
python archives/debug/debug_enhanced_preprocessing.py

# Validate specific model execution
python archives/test_scripts/test_saits_execution.py
```

## 📊 Model Performance

The system automatically evaluates model performance using multiple metrics:
- **MAE** (Mean Absolute Error)
- **R²** (Coefficient of Determination)
- **RMSE** (Root Mean Square Error)
- **Composite Score** (Weighted combination of metrics)

Models are automatically ranked based on their composite scores, making it easy to identify the best performing model for your specific dataset.

## 🔧 Development

### Adding New Models

To add a new model to the system:
1. Implement your model class in the `models/` directory
2. Add an entry to the `MODEL_REGISTRY` in `ml_core.py`
3. Ensure your model follows the expected interface

Example model registry entry:
```python
'my_new_model': {
    'type': 'shallow',  # or 'deep' or 'deep_advanced'
    'model_class': MyNewModelClass,
    'requires_sequences': False,  # or True for sequence models
    'config': {...}  # Model-specific configuration
}
```

### Extending Functionality

The modular architecture makes it easy to extend:
- Add new preprocessing steps in `data_handler.py`
- Implement new visualization types in `reporting.py`
- Add configuration options in `config_handler.py`

## 📈 Performance Benchmarks

The system has been tested with datasets containing:
- Up to 27,618+ samples
- Multiple wells with varying log suites
- Complex geological settings

GPU acceleration provides significant performance improvements:
- XGBoost: 5-10x faster training on GPU
- Deep learning models: 3-5x faster training with mixed precision
- Memory optimization: Enables processing of larger datasets

## 📁 Project Structure

The repository is organized for optimal development workflow:

### Core Pipeline (Root Directory)
```
├── main.py                        # Main entry point with GUI workflow
├── config_handler.py             # User interfaces and configuration
├── reporting.py                  # Visualization and performance reporting
├── ml_core_phase1_integration.py # Enhanced preprocessing integration
├── mlr_utils.py                  # Multiple linear regression utilities
├── requirements.txt              # Python dependencies
├── core_code/                    # Core pipeline modules
│   ├── data_handler.py          # LAS file operations and preprocessing
│   ├── ml_core.py               # Model registry and training pipeline
│   └── data_leakage_detector.py # Data validation utilities
└── preprocessing/                # Preprocessing modules
    ├── deep_model/              # Deep learning preprocessing
    │   ├── enhanced_preprocessing.py
    │   ├── integration_preprocessing.py
    │   ├── phase1_integration.py
    │   ├── phase1_preprocessing.py
    │   └── stability_preprocessing.py
    └── mlr_preprocessing.py     # MLR-specific preprocessing
```

### Essential Directories
```
├── models/                    # All model implementations
│   ├── advanced_models/       # SAITS, BRITS implementations
│   │   ├── saits_model.py     # SAITS model implementation
│   │   ├── brits_model.py     # BRITS model implementation
│   │   ├── base_model.py      # Base model class
│   │   └── utils/             # Model utilities
│   │       └── data_preparation.py
│   └── neuralnet.py          # Basic neural network models
├── utils/                     # Comprehensive utility modules
│   ├── gpu_acceleration.py   # GPU optimization and CUDA support
│   ├── memory_optimization.py # Memory management and OOM handling
│   ├── performance_monitor.py # Benchmarking and performance tracking
│   ├── display_utils.py       # Cross-platform display formatting
│   ├── mixed_precision_utils.py # Automatic Mixed Precision support
│   ├── training_optimization.py # Training loop optimizations
│   ├── xgboost_gpu_utils.py   # XGBoost GPU-specific utilities
│   ├── environment_setup.py   # Environment configuration
│   ├── tensorflow_compatibility.py # TensorFlow compatibility
│   ├── stability_core.py      # Core stability utilities
│   └── well_progress.py       # Progress tracking for well data
├── Las/                       # Input LAS well log files
├── config/                    # Configuration files
│   └── display_config.ini     # Display settings
├── plots/                     # Generated visualization outputs
└── example/                   # PyPOTS examples and tutorials
    ├── PyPOTS_Quick_Start.ipynb
    ├── Pypots_quick_start_tutor.py
    └── plots/                 # Example output plots
```

### Organized Archives and Documentation
```
├── archives/                  # Debugging and test utilities
│   ├── debug/                # Debug scripts and diagnostics
│   │   ├── saits_error_diagnostics_and_fixes.py
│   │   ├── debug_enhanced_preprocessing.py
│   │   ├── test_pipeline_optimization.py
│   │   ├── diagnose_data_insufficiency.py
│   │   └── test_fixes.py
│   └── test_scripts/         # Model testing and validation
│       ├── test_saits_execution.py
│       ├── test_option_a_reconstruction.py
│       └── main_fixed.py
└── docs/                      # Comprehensive documentation
    ├── fix_init/             # Implementation guides
    ├── fix_md/               # Fix documentation and summaries
    ├── optimization/         # Performance optimization guides
    └── archive_analysis/     # Historical analysis documentation
```

## 🛡️ Data Security & Privacy

- All processing happens locally on your machine
- No data is transmitted to external servers
- LAS files are only accessed during runtime
- Results are saved only to directories you specify
- Clean separation between active code and archived materials

## 🤝 Contributing

Contributions are welcome! Please:
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- XGBoost, LightGBM, and CatBoost teams for their excellent gradient boosting implementations
- PyPOTS team for the advanced deep learning models
- LASIO community for LAS file handling tools
- The broader Python data science ecosystem