#!/usr/bin/env python3
"""
Test script to validate SAITS error fixes.
This script tests the enhanced preprocessing and small dataset handling.
"""

import numpy as np
import pandas as pd
import sys
import os

# Add the current directory to the Python path
sys.path.insert(0, os.getcwd())

from core_code.data_handler import detect_small_dataset, create_sequences
from preprocessing.deep_model.enhanced_preprocessing import EnhancedLogPreprocessor

def create_test_dataset_small():
    """Create a small test dataset that mimics the problematic dataset."""
    print("Creating small test dataset...")

    # Create a small dataset with short valid intervals (like the problematic case)
    np.random.seed(42)
    n_rows = 72  # Similar to the small validation split that was failing
    n_wells = 1

    # Create depth data
    depths = np.linspace(1000, 1100, n_rows)

    # Create feature data with some missing values to create short intervals
    data = {
        'MD': depths,
        'WELL': [f'WELL_01'] * n_rows,
        'GR': np.random.normal(60, 20, n_rows),
        'RHOB': np.random.normal(2.4, 0.3, n_rows),
        'NPHI': np.random.normal(0.2, 0.1, n_rows)
    }

    df = pd.DataFrame(data)

    # Introduce missing values to create short intervals (like real well log data)
    missing_indices = np.random.choice(n_rows, size=int(0.3 * n_rows), replace=False)
    df.loc[missing_indices, ['GR', 'RHOB', 'NPHI']] = np.nan

    print(f"Created test dataset: {len(df)} rows, {len(df.WELL.unique())} wells")
    print(f"Missing data ratio: {(df[['GR', 'RHOB', 'NPHI']].isna().any(axis=1).sum()) / len(df):.1%}")

    return df

def test_small_dataset_detection():
    """Test the small dataset detection functionality."""
    print("\n=== Testing Small Dataset Detection ===")

    df_small = create_test_dataset_small()

    # Test small dataset detection
    dataset_info = detect_small_dataset(df_small, 'WELL', sequence_len=64)

    print(f"Dataset info: {dataset_info}")

    assert dataset_info['is_small_dataset'], "Should detect small dataset"
    assert dataset_info['recommended_sequence_len'] <= 8, "Should recommend small sequence length"
    assert dataset_info['total_rows'] == 72, "Should have 72 rows"

    print("✅ Small dataset detection working correctly")

def test_sequence_creation_small_dataset():
    """Test sequence creation with small dataset."""
    print("\n=== Testing Sequence Creation with Small Dataset ===")

    df_small = create_test_dataset_small()
    feature_cols = ['GR', 'RHOB', 'NPHI']

    # Test with various sequence lengths
    for seq_len in [64, 32, 16, 8, 4, 2, 1]:
        try:
            sequences, metadata = create_sequences(
                df_small, 'WELL', feature_cols,
                sequence_len=seq_len, step=1, use_enhanced=True
            )

            if sequences.size > 0:
                print(f"✅ Sequence length {seq_len}: Created {len(sequences)} sequences")
                print(f"   Sequence shape: {sequences.shape}")
                break
            else:
                print(f"⚠️ Sequence length {seq_len}: No sequences created")

        except Exception as e:
            print(f"❌ Sequence length {seq_len}: Error - {str(e)[:100]}...")

def test_enhanced_preprocessing_with_small_sequences():
    """Test enhanced preprocessing with very small sequences."""
    print("\n=== Testing Enhanced Preprocessing with Small Sequences ===")

    df_small = create_test_dataset_small()
    feature_cols = ['GR', 'RHOB', 'NPHI']

    for seq_len in [4, 2, 1]:
        try:
            preprocessor = EnhancedLogPreprocessor(sequence_len=seq_len, sequence_stride=1)
            sequences, metadata = preprocessor.create_sequences_enhanced(df_small, 'WELL', feature_cols)

            if sequences.size > 0:
                print(f"✅ Enhanced preprocessing with seq_len {seq_len}: {len(sequences)} sequences")
                print(f"   Sequence shape: {sequences.shape}")
            else:
                print(f"⚠️ Enhanced preprocessing with seq_len {seq_len}: No sequences")

        except Exception as e:
            print(f"❌ Enhanced preprocessing with seq_len {seq_len}: Error - {str(e)[:100]}...")

def test_gap_tolerance():
    """Test gap tolerance functionality."""
    print("\n=== Testing Gap Tolerance Functionality ===")

    df = create_test_dataset_small()
    feature_cols = ['GR', 'RHOB', 'NPHI']

    preprocessor = EnhancedLogPreprocessor(sequence_len=4, sequence_stride=1)

    # Test with different gap tolerance values
    for gap_tolerance in [0, 1, 2, 3]:
        intervals = preprocessor.get_valid_intervals(
            df[feature_cols].values,
            min_interval_length=2,
            gap_tolerance=gap_tolerance
        )

        print(f"Gap tolerance {gap_tolerance}: Found {len(intervals)} valid intervals")

        if len(intervals) > 0:
            avg_length = np.mean([end - start for start, end in intervals])
            print(f"  Average interval length: {avg_length:.1f}")

def main():
    """Run all tests."""
    print("SAITS Error Fix Validation Tests")
    print("=" * 50)

    try:
        test_small_dataset_detection()
        test_sequence_creation_small_dataset()
        test_enhanced_preprocessing_with_small_sequences()
        test_gap_tolerance()

        print("\n" + "=" * 50)
        print("✅ All tests completed successfully!")
        print("The SAITS error fixes appear to be working correctly.")
        print("\nKey improvements implemented:")
        print("1. ✅ Extended fallback sequence lengths [4, 2, 1]")
        print("2. ✅ Small dataset detection and handling")
        print("3. ✅ Enhanced preprocessing with gap tolerance")
        print("4. ✅ Improved error handling in ml_core.py")

    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)