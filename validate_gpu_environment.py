#!/usr/bin/env python3
"""
GPU Environment Validation for SAITS/PyPOTS

Run this script to verify TensorFlow and PyPOTS installation, and to check
GPU availability and basic SAITS model creation.
"""

from __future__ import annotations

import sys


def validate_gpu_environment() -> bool:
    """Comprehensive GPU environment validation for SAITS/PyPOTS."""
    print("\n🔍 GPU Environment Validation for SAITS/PyPOTS")
    print("=" * 60)

    # Check TensorFlow
    try:
        import tensorflow as tf
        print(f"✅ TensorFlow {tf.__version__} installed")

        # Check GPU devices
        gpus = tf.config.list_physical_devices('GPU')
        if gpus:
            print(f"🚀 {len(gpus)} GPU(s) detected by Tensor<PERSON>low:")
            for i, gpu in enumerate(gpus):
                name = getattr(gpu, 'name', f'GPU:{i}')
                print(f"   • GPU {i}: {name}")
        else:
            print("💻 No GPUs detected - will use CPU")

    except ImportError:
        print("❌ TensorFlow not installed")
        print("   💡 Install with: pip install 'tensorflow>=2.10.0,<2.16.0'")
        return False
    except Exception as e:
        print(f"⚠️ TensorFlow check error: {e}")

    # Check PyPOTS
    try:
        import pypots
        print(f"✅ PyPOTS {getattr(pypots, '__version__', 'unknown')} installed")
    except ImportError:
        print("❌ PyPOTS not installed")
        print("   💡 Install with: pip install 'pypots>=0.2.0'")
        return False

    # Test SAITS creation
    try:
        from pypots.imputation import SAITS
        from pypots.optim import Adam
        # Use proper SAITS parameters based on PyPOTS API
        test_model = SAITS(
            n_steps=10,
            n_features=2,
            n_layers=1,
            d_model=64,
            n_heads=4,
            d_k=16,
            d_v=16,
            d_ffn=256,
            epochs=1,
            optimizer=Adam(lr=1e-3),
            device='cpu'
        )
        print("✅ SAITS model creation successful")
        return True
    except Exception as e:
        print(f"❌ SAITS model creation failed: {e}")
        return False


if __name__ == "__main__":
    ok = validate_gpu_environment()
    sys.exit(0 if ok else 1)

