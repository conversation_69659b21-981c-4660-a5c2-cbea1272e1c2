Enhanced sequences shape: (1179, 16, 6), type: <class 'numpy.ndarray'>, dtype: float64
Using enhanced normalization with winsorization...
📊 Small dataset detected:
   - Total rows: 13772
   - Wells: 6
   - Avg rows/well: 2295.3
   - Valid data ratio: 0.0%
   - Recommended sequence length: 16
   - Recommended step: 16
🔧 Adjusted parameters for small dataset:
   - Sequence length: 64 → 16
   - Step: 1 → 16
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: TRAINING
Creating sequences | Current: B-L-6: 100%|████████████████████| 6/6 wells [00:00<00:00, 54.11well/s]    

📊 Well Processing Summary:
   • Total wells processed: 6
   • Successful: 6
   • Failed: 0
   • Total valid intervals: 6
   • Total sequences created: 391
Enhanced sequences shape: (391, 16, 6), type: <class 'numpy.ndarray'>, dtype: float64
📚 Introducing missingness for imputation training.
Using enhanced missing value introduction with realistic patterns...
Introduced 20.0% missing values (22595 elements)
Pattern: 10186 random + 12409 chunked
Enhanced missing sequences shape: (1179, 16, 6), type: <class 'numpy.ndarray'>, dtype: float64
Using enhanced missing value introduction with realistic patterns...
Introduced 19.9% missing values (7460 elements)
Pattern: 3378 random + 4082 chunked
Enhanced missing sequences shape: (391, 16, 6), type: <class 'numpy.ndarray'>, dtype: float64
Converting sequences to tensors...
   Processing train_sequences_missing: current type is <class 'numpy.ndarray'>
   Changing train_sequences_missing dtype from float64 to float32.
   train_sequences_missing tensor created with shape: torch.Size([1179, 16, 6]), dtype: torch.float32   
   Processing train_sequences_true: current type is <class 'numpy.ndarray'>
   Changing train_sequences_true dtype from float64 to float32.
   train_sequences_true tensor created with shape: torch.Size([1179, 16, 6]), dtype: torch.float32      
   Processing val_sequences_missing: current type is <class 'numpy.ndarray'>
   Changing val_sequences_missing dtype from float64 to float32.
   val_sequences_missing tensor created with shape: torch.Size([391, 16, 6]), dtype: torch.float32      
   Processing val_sequences_true: current type is <class 'numpy.ndarray'>
   Changing val_sequences_true dtype from float64 to float32.
   val_sequences_true tensor created with shape: torch.Size([391, 16, 6]), dtype: torch.float32
   Tensor shapes - Train: torch.Size([1179, 16, 6]), Truth: torch.Size([1179, 16, 6])
   📏 Detected actual sequence length: 16
🔧 Applied fixed parameters for SAITS (Self-Attention): {}
🔍 Pre-checking PyPOTS availability for SAITS (Self-Attention) model...
🔄 TensorFlow cache reset - will re-check availability
🔧 TensorFlow environment configured for compatibility (TF 2.15.1 available)
✅ TensorFlow 2.15.1 loaded (CPU-only)
✅ PyPOTS imported successfully with TensorFlow backend
✅ PyPOTS availability confirmed for SAITS (Self-Attention) model
Initialized SAITSModel with:
   - Features: 6
   - Sequence length: 16
   - Epochs: 40
   - Batch size: 64
   - Learning rate: 0.002

🔍 Performing comprehensive SAITS environment validation...

📋 Step 1: Environment Validation
✅ PyPOTS imported successfully with TensorFlow backend
   TensorFlow: available
   PyPOTS: available
   GPU: not_available

💡 Recommendations:
   💻 CPU-only mode - consider GPU setup
   ✅ PyPOTS ready for SAITS model

📋 Step 2: TensorFlow GPU Configuration
💻 No GPUs detected - using CPU mode
💻 CPU mode configured - consider GPU setup for better performance

💡 Performance Optimization Tips:
   💻 CPU-only mode detected
   💡 Consider installing CUDA-compatible TensorFlow for GPU acceleration
   💡 Use smaller batch sizes to avoid memory issues
   💡 Consider using distributed training for large datasets
✅ TensorFlow 2.15.1 ready

📋 Step 3: PyPOTS Import and Configuration
✅ PyPOTS imported successfully with TensorFlow backend
✅ PyPOTS components successfully imported: pypots=0.19, SAITS=SAITS

🎯 SAITS Model Ready for Training!
   💻 CPU mode - training will be slower but functional
🚀 Selected GPU 0: NVIDIA T550 Laptop GPU
   Memory: 4.3 GB
   Compute Capability: 7.5
ℹ️ TF32 available but not enabled (pre-Ampere GPU)
🔥 SDPA optimized backends enabled (Flash Attention support)
⚡ Mixed precision training available
✅ GPU initialized successfully
🚀 SAITS using GPU with fallback protection: cuda
✅ SAITS (Self-Attention) model created successfully
🔧 Forcing model initialization to prepare optimizer for schedulers...
🔧 Initializing SAITS model...
2025-08-20 22:54:07 [INFO]: Using the given device: cuda
2025-08-20 22:54:07 [WARNING]: ‼️ saving_path not given. Model files and tensorboard file will not be saaved.
2025-08-20 22:54:07 [INFO]: Using customized MAE as the training loss function.
2025-08-20 22:54:07 [INFO]: Using customized MSE as the validation metric function.
2025-08-20 22:54:07 [INFO]: SAITS initialized with the given hyperparameters, the number of trainable parameters: 3,164,864
✅ SAITS model initialized successfully
   - Parameters: ~662,528
   - Memory usage: ~4.5 MB
Training phase...
   About to train model with:
   - train_tensor type: <class 'torch.Tensor'>, shape: torch.Size([1179, 16, 6]), dtype: torch.float32  
   - truth_tensor type: <class 'torch.Tensor'>, shape: torch.Size([1179, 16, 6]), dtype: torch.float32  
🔧 Initializing training stability utilities...
INFO:preprocessing.deep_model.stability_preprocessing:🔧 Initialized gradient clipper for saits with max_norm=1.0
   ℹ️ Skipping AdaptiveLRScheduler for PyPOTS model (saits); optimizer is managed internally.
   ✅ Gradient clipper (saits) initialized
Training PyPOTS-based model: SAITS (Self-Attention)
   Using tensor interface with batch_size=64, epochs=40
📊 Performance monitoring started
📊 Performance monitoring started for SAITS training
⚡ Mixed precision training enabled
🚀 Mixed precision training enabled via GPUManager (enhanced)
2025-08-20 22:54:10 [INFO]: Epoch 001 - training loss (MAE): 1.1630
2025-08-20 22:54:10 [INFO]: Epoch 002 - training loss (MAE): 0.7493
2025-08-20 22:54:11 [INFO]: Epoch 003 - training loss (MAE): 0.6781
2025-08-20 22:54:11 [INFO]: Epoch 004 - training loss (MAE): 0.6420
2025-08-20 22:54:12 [INFO]: Epoch 005 - training loss (MAE): 0.6141
2025-08-20 22:54:13 [INFO]: Epoch 006 - training loss (MAE): 0.5816
2025-08-20 22:54:13 [INFO]: Epoch 007 - training loss (MAE): 0.5567
2025-08-20 22:54:14 [INFO]: Epoch 008 - training loss (MAE): 0.5378
2025-08-20 22:54:14 [INFO]: Epoch 009 - training loss (MAE): 0.5333
2025-08-20 22:54:15 [INFO]: Epoch 010 - training loss (MAE): 0.5167
2025-08-20 22:54:15 [INFO]: Epoch 011 - training loss (MAE): 0.5020
2025-08-20 22:54:16 [INFO]: Epoch 012 - training loss (MAE): 0.4888
2025-08-20 22:54:17 [INFO]: Epoch 013 - training loss (MAE): 0.5015
2025-08-20 22:54:17 [INFO]: Epoch 014 - training loss (MAE): 0.4821
2025-08-20 22:54:18 [INFO]: Epoch 015 - training loss (MAE): 0.4681
2025-08-20 22:54:18 [INFO]: Epoch 016 - training loss (MAE): 0.4603
2025-08-20 22:54:19 [INFO]: Epoch 017 - training loss (MAE): 0.4586
2025-08-20 22:54:20 [INFO]: Epoch 018 - training loss (MAE): 0.4526
2025-08-20 22:54:20 [INFO]: Epoch 019 - training loss (MAE): 0.4478
2025-08-20 22:54:21 [INFO]: Epoch 020 - training loss (MAE): 0.4461
2025-08-20 22:54:21 [INFO]: Epoch 021 - training loss (MAE): 0.4390
2025-08-20 22:54:22 [INFO]: Epoch 022 - training loss (MAE): 0.4361
2025-08-20 22:54:22 [INFO]: Epoch 023 - training loss (MAE): 0.4569
2025-08-20 22:54:23 [INFO]: Epoch 024 - training loss (MAE): 0.4345
2025-08-20 22:54:24 [INFO]: Epoch 025 - training loss (MAE): 0.4270
2025-08-20 22:54:24 [INFO]: Epoch 026 - training loss (MAE): 0.4210
2025-08-20 22:54:25 [INFO]: Epoch 027 - training loss (MAE): 0.4297
2025-08-20 22:54:25 [INFO]: Epoch 028 - training loss (MAE): 0.4244
2025-08-20 22:54:26 [INFO]: Epoch 029 - training loss (MAE): 0.4197
2025-08-20 22:54:26 [INFO]: Epoch 030 - training loss (MAE): 0.4171
2025-08-20 22:54:27 [INFO]: Epoch 031 - training loss (MAE): 0.4187
2025-08-20 22:54:28 [INFO]: Epoch 032 - training loss (MAE): 0.4196
2025-08-20 22:54:28 [INFO]: Epoch 033 - training loss (MAE): 0.4114
2025-08-20 22:54:29 [INFO]: Epoch 034 - training loss (MAE): 0.4221
2025-08-20 22:54:29 [INFO]: Epoch 035 - training loss (MAE): 0.4083
2025-08-20 22:54:30 [INFO]: Epoch 036 - training loss (MAE): 0.4176
2025-08-20 22:54:31 [INFO]: Epoch 037 - training loss (MAE): 0.4106
2025-08-20 22:54:31 [INFO]: Epoch 038 - training loss (MAE): 0.4018
2025-08-20 22:54:32 [INFO]: Epoch 039 - training loss (MAE): 0.4031
2025-08-20 22:54:32 [INFO]: Epoch 040 - training loss (MAE): 0.4089
2025-08-20 22:54:32 [INFO]: Finished training. The best model is from epoch#38.
   ⏱️ Epoch time: 24.84s, Memory Δ: +67.7MB
📊 Performance monitoring stopped

📊 SAITS Training Performance Summary:
   • Total Training Time: 24.8s
Enhanced Evaluation Phase...
📦 Small dataset (391 samples), using standard prediction
Imputation Metrics (Artificial Missing Values):
   • MAE: 0.2281
   • R²: 0.6615
   • RMSE: 0.3054
   • Evaluated Points: 1264
📊 Small dataset detected:
   - Total rows: 13772
   - Wells: 6
   - Avg rows/well: 2295.3
   - Valid data ratio: 0.0%
   - Recommended sequence length: 16
   - Recommended step: 4
🔧 Adjusted parameters for small dataset:
   - Sequence length: 16 → 16
   - Step: 1 → 4
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: PREDICTION
  - Using feature columns only for valid interval detection
  - Target column will be included in sequences as NaN for model prediction
Creating sequences | Current: B-L-6: 100%|████████████████████| 6/6 wells [00:00<00:00, 14.31well/s]    

📊 Well Processing Summary:
   • Total wells processed: 6
   • Successful: 6
   • Failed: 0
   • Total valid intervals: 6
   • Total sequences created: 1,560
Enhanced sequences shape: (1560, 16, 6), type: <class 'numpy.ndarray'>, dtype: float64
🧠 Large dataset detected (1,560 samples), using memory optimization

==================================================
[MEM] MEMORY STATUS
==================================================
System Memory:
   * Total: 31.7 GB
   * Available: 12.3 GB
   * Usage: 61.3%

GPU Memory:
   * Total: 4.0 GB
   * Allocated: 0.1 GB
   * Reserved: 0.1 GB
   * Free: 3.9 GB
==================================================
🧠 Memory-optimized prediction for 1,560 samples
[MEM] Calculated optimal batch size: 32
   * Available memory: 3215.2 MB
   * Estimated usage: 30.5 MB
   • Using batch size: 32
SAITS Prediction: 100%|█████████████████████████████████████████████| 49/49 [00:16<00:00,  3.02batch/s] 
✅ SAITS Prediction: 100% [██████████████████████████] Done
✅ Batch alignment verified: 1560 predictions for 1560 input samples

📊 Memory status after processing:

==================================================
[MEM] MEMORY STATUS
==================================================
System Memory:
   * Total: 31.7 GB
   * Available: 12.3 GB
   * Usage: 61.2%

GPU Memory:
   * Total: 4.0 GB
   * Allocated: 0.1 GB
   * Reserved: 0.1 GB
   * Free: 3.9 GB
==================================================
⚠️ Optimization failed after 47.12s: operands could not be broadcast together with shapes (6256,) (249600,)
   🔄 Falling back to original implementation...
🚀 Starting Phase 1 Enhanced Deep Learning Training...
   Model: SAITS (Self-Attention)
   Target: P-WAVE
   Features: ['GR', 'NPHI', 'RHOB', 'RT', 'TVDSS']

📊 Step 1: Initial Data Preparation...
Using enhanced normalization with winsorization...
Normalized 'GR': method=standard, valid_data=51983/57856 (89.8%)
Normalized 'NPHI': method=standard, valid_data=42700/57856 (73.8%)
Normalized 'RHOB': method=standard, valid_data=36835/57856 (63.7%)
Normalized 'RT': method=standard, valid_data=47696/57856 (82.4%)
Normalized 'TVDSS': method=standard, valid_data=55307/57856 (95.6%)
Normalized 'P-WAVE': method=standard, valid_data=37801/57856 (65.3%)
📊 Small dataset detected:
   - Total rows: 57856
   - Wells: 8
   - Avg rows/well: 7232.0
   - Valid data ratio: 6.4%
   - Recommended sequence length: 16
   - Recommended step: 16
🔧 Adjusted parameters for small dataset:
   - Sequence length: 64 → 16
   - Step: 1 → 16
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: TRAINING
Creating sequences | Current: EB-1: 100%|█████████████████████| 8/8 wells [00:00<00:00, 15.09well/s]    

📊 Well Processing Summary:
   • Total wells processed: 8
   • Successful: 8
   • Failed: 0
   • Total valid intervals: 23
   • Total sequences created: 2,177
Enhanced sequences shape: (2177, 16, 6), type: <class 'numpy.ndarray'>, dtype: float64
   Created 2177 sequences
   Sequence shape: (2177, 16, 6)

🔍 Step 2: Phase 1 Advanced Preprocessing...
   Dataset characteristics:
     • Sequences: 2177
     • Missing rate: 0.0%
     • Recommended config: {'normalization_method': 'robust_standard', 'missing_encoding_method': 'learnable_embedding', 'validate_ranges': True}
INFO:preprocessing.deep_model.stability_preprocessing:🚀 Starting Phase 1 Advanced Preprocessing Pipeline...
INFO:preprocessing.deep_model.stability_preprocessing:
🔍 Step 1: Advanced Input Validation & Cleaning...
INFO:preprocessing.deep_model.stability_preprocessing:
❓ Step 2: Missing Value Encoding (learnable_embedding)...
🔧 Step 3.5: Encoding artificial missing values for imputation training...
   Method: learnable_embedding
   Missing values before: 0 -> After: 0
   Missing rate before: 0.0%
INFO:preprocessing.deep_model.stability_preprocessing:
📊 Step 3: Robust Normalization (robust_standard)...
INFO:preprocessing.deep_model.stability_preprocessing:
🔧 Step 4: Final Numerical Stability Check...
INFO:preprocessing.deep_model.stability_preprocessing:✅ Final sequences pass all stability checks      
INFO:preprocessing.deep_model.stability_preprocessing:
🎉 Phase 1 preprocessing pipeline completed successfully!
INFO:preprocessing.deep_model.stability_preprocessing:   Input shape: (2177, 16, 6) → Output shape: (2177, 16, 6)
INFO:preprocessing.deep_model.stability_preprocessing:   Data quality score: 0.950
INFO:preprocessing.deep_model.stability_preprocessing:   Ready for stable deep learning training!       
✅ Phase 1 preprocessing completed:
   • Data quality score: 0.950
   • Missing rate: 0.0% → 0.0%
   • Final stability: ✅ STABLE

🎯 Step 3: Preparing Training Sequences...
   📚 IMPUTATION MODE: Creating training sequences with missing values
Using enhanced missing value introduction with realistic patterns...
Introduced 20.1% missing values (41979 elements)
Pattern: 18809 random + 23170 chunked
Enhanced missing sequences shape: (2177, 16, 6), type: <class 'numpy.ndarray'>, dtype: float64
   Training sequences with missing values: (2177, 16, 6)

🔧 Step 3.5: Encoding artificial missing values for imputation training...
🔧 Step 3.5: Encoding artificial missing values for imputation training...
   Method: masking_tokens
   Missing values before: 41979 -> After: 0
   Missing rate before: 20.1%
   ✅ Final training sequences encoded. Missing values are now represented as tokens.
   Missing values before: 41979 -> After: 0

🔧 Step 4: Enhanced Pre-training Validation (Imputation Mode)...
   ✅ Training sequences validated: 20.1% missing rate, 167,013 finite values
   Training sequences: ✅ STABLE
   Truth sequences: ✅ STABLE

🚀 Step 5: Enhanced Model Training...
   Training tensor shape: torch.Size([2177, 16, 6])
   Truth tensor shape: torch.Size([2177, 16, 6])
   Missing values in training: 41979
   Missing values in truth: 0
   Preparing enhanced data for model training...
   Created processed DataFrame: (34832, 8)
   Wells: 218

🎯 Calling enhanced model training...
--- Running Deep Learning Model: SAITS (Self-Attention) ---
CuDNN benchmark enabled for performance.
Automatic well assignment:
  - Training/Validation wells: ['WELL_100', 'WELL_215', 'WELL_139', 'WELL_178', 'WELL_15', 'WELL_154', 'WELL_170', 'WELL_73', 'WELL_207', 'WELL_140', 'WELL_189', 'WELL_30', 'WELL_211', 'WELL_9', 'WELL_67', 'WELL_177', 'WELL_173', 'WELL_18', 'WELL_155', 'WELL_66', 'WELL_192', 'WELL_143', 'WELL_79', 'WELL_25', 'WELL_113', 'WELL_147', 'WELL_176', 'WELL_16', 'WELL_45', 'WELL_152', 'WELL_109', 'WELL_55', 'WELL_213', 'WELL_194', 'WELL_108', 'WELL_162', 'WELL_75', 'WELL_136', 'WELL_119', 'WELL_203', 'WELL_96', 'WELL_185', 'WELL_118', 'WELL_93', 'WELL_84', 'WELL_95', 'WELL_111', 'WELL_125', 'WELL_86', 'WELL_164', 'WELL_104', 'WELL_38', 'WELL_24', 'WELL_167', 'WELL_208', 'WELL_29', 'WELL_19', 'WELL_69', 'WELL_97', 'WELL_60', 'WELL_68', 'WELL_5', 'WELL_137', 'WELL_56', 'WELL_101', 'WELL_132', 'WELL_82', 'WELL_65', 'WELL_138', 'WELL_184', 'WELL_31', 'WELL_12', 'WELL_35', 'WELL_28', 'WELL_42', 'WELL_161', 'WELL_112', 'WELL_148', 'WELL_51', 'WELL_122', 'WELL_175', 'WELL_76', 'WELL_41', 'WELL_201', 'WELL_78', 'WELL_128', 'WELL_26', 'WELL_144', 'WELL_120', 'WELL_126', 'WELL_2', 'WELL_77', 'WELL_46', 'WELL_153', 'WELL_204', 'WELL_90', 'WELL_85', 'WELL_146', 'WELL_98', 'WELL_36', 'WELL_135', 'WELL_61', 'WELL_22', 'WELL_142', 'WELL_115', 'WELL_33', 'WELL_11', 'WELL_212', 'WELL_172', 'WELL_6', 'WELL_27', 'WELL_181', 'WELL_209', 'WELL_199', 'WELL_4', 'WELL_32', 'WELL_117', 'WELL_195', 'WELL_124', 'WELL_156', 'WELL_114', 'WELL_10', 'WELL_62', 'WELL_127', 'WELL_158', 'WELL_216', 'WELL_0', 'WELL_196', 'WELL_165', 'WELL_70', 'WELL_168', 'WELL_64', 'WELL_44', 'WELL_141', 'WELL_40', 'WELL_123', 'WELL_23', 'WELL_163', 'WELL_159', 'WELL_81', 'WELL_39', 'WELL_182', 'WELL_47', 'WELL_94', 'WELL_171', 'WELL_43', 'WELL_145', 'WELL_150', 'WELL_3', 'WELL_105', 'WELL_53', 'WELL_133', 'WELL_197', 'WELL_183', 'WELL_206', 'WELL_49', 'WELL_80', 'WELL_34', 'WELL_7', 'WELL_110', 'WELL_91', 'WELL_83', 'WELL_193', 'WELL_198', 'WELL_89', 'WELL_8', 'WELL_13', 'WELL_59', 'WELL_186', 'WELL_131', 'WELL_17', 'WELL_166', 'WELL_72', 'WELL_190']
  - Test wells: ['WELL_134', 'WELL_180', 'WELL_200', 'WELL_63', 'WELL_54', 'WELL_107', 'WELL_50', 'WELL_174', 'WELL_214', 'WELL_169', 'WELL_58', 'WELL_48', 'WELL_88', 'WELL_21', 'WELL_57', 'WELL_160', 'WELL_210', 'WELL_187', 'WELL_191', 'WELL_129', 'WELL_37', 'WELL_157', 'WELL_205', 'WELL_1', 'WELL_52', 'WELL_149', 'WELL_130', 'WELL_151', 'WELL_103', 'WELL_99', 'WELL_116', 'WELL_87', 'WELL_202', 'WELL_74', 'WELL_121', 'WELL_217', 'WELL_20', 'WELL_188', 'WELL_71', 'WELL_106', 'WELL_14', 'WELL_92', 'WELL_179', 'WELL_102']
   Using adaptive validation ratio: 30.0% (median well size: 160)
   Minimum well size threshold: 12 (validation ratio: 30.0%)
Flexible Split Report:
  - Wells for Training/Validation: 174
  - Wells for Final Testing: 44
  - Train Samples (Shallow part of train wells): 19488
  - Validation Samples (Deeper part of train wells): 8352
  - Test Samples (Entirely separate wells): 6992

Running data leakage detection on splits...
🔍 Performing comprehensive data leakage check...
============================================================
🔍 Detecting perfect correlation leakage...
✅ No perfect correlation leakage detected
🕐 Validating temporal split integrity...
✅ No temporal leakage detected in splits
🎯 Detecting target leakage in features...
✅ No target leakage detected in features
============================================================
📊 COMPREHENSIVE LEAKAGE CHECK SUMMARY
============================================================
✅ NO DATA LEAKAGE DETECTED!
   All checks passed successfully
   Data quality score: 1.00
============================================================

Using enhanced normalization with winsorization...
Normalized 'GR': method=standard, valid_data=19488/19488 (100.0%)
Normalized 'NPHI': method=standard, valid_data=19488/19488 (100.0%)
Normalized 'RHOB': method=standard, valid_data=19488/19488 (100.0%)
Normalized 'RT': method=standard, valid_data=19488/19488 (100.0%)
Normalized 'TVDSS': method=standard, valid_data=19488/19488 (100.0%)
Normalized 'P-WAVE': method=standard, valid_data=19488/19488 (100.0%)
📊 Small dataset detected:
   - Total rows: 19488
   - Wells: 174
   - Avg rows/well: 112.0
   - Valid data ratio: 100.0%
   - Recommended sequence length: 16
   - Recommended step: 16
🔧 Adjusted parameters for small dataset:
   - Sequence length: 64 → 16
   - Step: 1 → 16
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: TRAINING
Creating sequences | Current: WELL_190: 100%|████████████| 174/174 wells [00:00<00:00, 294.54well/s]    

📊 Well Processing Summary:
   • Total wells processed: 174
   • Successful: 174
   • Failed: 0
   • Total valid intervals: 174
   • Total sequences created: 1,218
Enhanced sequences shape: (1218, 16, 6), type: <class 'numpy.ndarray'>, dtype: float64
Using enhanced normalization with winsorization...
📊 Small dataset detected:
   - Total rows: 8352
   - Wells: 174
   - Avg rows/well: 48.0
   - Valid data ratio: 100.0%
   - Recommended sequence length: 16
   - Recommended step: 16
🔧 Adjusted parameters for small dataset:
   - Sequence length: 64 → 16
   - Step: 1 → 16
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: TRAINING
Creating sequences | Current: WELL_190: 100%|████████████| 174/174 wells [00:00<00:00, 516.94well/s]    

📊 Well Processing Summary:
   • Total wells processed: 174
   • Successful: 174
   • Failed: 0
   • Total valid intervals: 174
   • Total sequences created: 522
Enhanced sequences shape: (522, 16, 6), type: <class 'numpy.ndarray'>, dtype: float64
📚 Introducing missingness for imputation training.
Using enhanced missing value introduction with realistic patterns...
Introduced 20.1% missing values (23556 elements)
Pattern: 10523 random + 13033 chunked
Enhanced missing sequences shape: (1218, 16, 6), type: <class 'numpy.ndarray'>, dtype: float64
Using enhanced missing value introduction with realistic patterns...
Introduced 20.3% missing values (10179 elements)
Pattern: 4509 random + 5670 chunked
Enhanced missing sequences shape: (522, 16, 6), type: <class 'numpy.ndarray'>, dtype: float64
Converting sequences to tensors...
   Processing train_sequences_missing: current type is <class 'numpy.ndarray'>
   Changing train_sequences_missing dtype from float64 to float32.
   train_sequences_missing tensor created with shape: torch.Size([1218, 16, 6]), dtype: torch.float32   
   Processing train_sequences_true: current type is <class 'numpy.ndarray'>
   Changing train_sequences_true dtype from float64 to float32.
   train_sequences_true tensor created with shape: torch.Size([1218, 16, 6]), dtype: torch.float32      
   Processing val_sequences_missing: current type is <class 'numpy.ndarray'>
   Changing val_sequences_missing dtype from float64 to float32.
   val_sequences_missing tensor created with shape: torch.Size([522, 16, 6]), dtype: torch.float32      
   Processing val_sequences_true: current type is <class 'numpy.ndarray'>
   Changing val_sequences_true dtype from float64 to float32.
   val_sequences_true tensor created with shape: torch.Size([522, 16, 6]), dtype: torch.float32
   Tensor shapes - Train: torch.Size([1218, 16, 6]), Truth: torch.Size([1218, 16, 6])
   📏 Detected actual sequence length: 16
🔧 Applied fixed parameters for SAITS (Self-Attention): {}
🔍 Pre-checking PyPOTS availability for SAITS (Self-Attention) model...
🔄 TensorFlow cache reset - will re-check availability
🔧 TensorFlow environment configured for compatibility (TF 2.15.1 available)
✅ TensorFlow 2.15.1 loaded (CPU-only)
✅ PyPOTS imported successfully with TensorFlow backend
✅ PyPOTS availability confirmed for SAITS (Self-Attention) model
Initialized SAITSModel with:
   - Features: 6
   - Sequence length: 16
   - Epochs: 40
   - Batch size: 64
   - Learning rate: 0.002

🔍 Performing comprehensive SAITS environment validation...

📋 Step 1: Environment Validation
✅ PyPOTS imported successfully with TensorFlow backend
   TensorFlow: available
   PyPOTS: available
   GPU: not_available

💡 Recommendations:
   💻 CPU-only mode - consider GPU setup
   ✅ PyPOTS ready for SAITS model

📋 Step 2: TensorFlow GPU Configuration
💻 No GPUs detected - using CPU mode
💻 CPU mode configured - consider GPU setup for better performance

💡 Performance Optimization Tips:
   💻 CPU-only mode detected
   💡 Consider installing CUDA-compatible TensorFlow for GPU acceleration
   💡 Use smaller batch sizes to avoid memory issues
   💡 Consider using distributed training for large datasets
✅ TensorFlow 2.15.1 ready

📋 Step 3: PyPOTS Import and Configuration
✅ PyPOTS imported successfully with TensorFlow backend
✅ PyPOTS components successfully imported: pypots=0.19, SAITS=SAITS

🎯 SAITS Model Ready for Training!
   💻 CPU mode - training will be slower but functional
🚀 Selected GPU 0: NVIDIA T550 Laptop GPU
   Memory: 4.3 GB
   Compute Capability: 7.5
ℹ️ TF32 available but not enabled (pre-Ampere GPU)
🔥 SDPA optimized backends enabled (Flash Attention support)
⚡ Mixed precision training available
✅ GPU initialized successfully
🚀 SAITS using GPU with fallback protection: cuda
✅ SAITS (Self-Attention) model created successfully
🔧 Forcing model initialization to prepare optimizer for schedulers...
🔧 Initializing SAITS model...
2025-08-20 22:54:52 [INFO]: Using the given device: cuda
2025-08-20 22:54:52 [WARNING]: ‼️ saving_path not given. Model files and tensorboard file will not be saaved.
2025-08-20 22:54:52 [INFO]: Using customized MAE as the training loss function.
2025-08-20 22:54:52 [INFO]: Using customized MSE as the validation metric function.
2025-08-20 22:54:52 [INFO]: SAITS initialized with the given hyperparameters, the number of trainable parameters: 3,164,864
✅ SAITS model initialized successfully
   - Parameters: ~662,528
   - Memory usage: ~4.5 MB
Training phase...
   About to train model with:
   - train_tensor type: <class 'torch.Tensor'>, shape: torch.Size([1218, 16, 6]), dtype: torch.float32  
   - truth_tensor type: <class 'torch.Tensor'>, shape: torch.Size([1218, 16, 6]), dtype: torch.float32  
🔧 Initializing training stability utilities...
INFO:preprocessing.deep_model.stability_preprocessing:🔧 Initialized gradient clipper for saits with max_norm=1.0
   ℹ️ Skipping AdaptiveLRScheduler for PyPOTS model (saits); optimizer is managed internally.
   ✅ Gradient clipper (saits) initialized
Training PyPOTS-based model: SAITS (Self-Attention)
   Using tensor interface with batch_size=64, epochs=40
📊 Performance monitoring started
📊 Performance monitoring started for SAITS training
⚡ Mixed precision training enabled
🚀 Mixed precision training enabled via GPUManager (enhanced)
2025-08-20 22:54:53 [INFO]: Epoch 001 - training loss (MAE): 1.3060
2025-08-20 22:54:54 [INFO]: Epoch 002 - training loss (MAE): 0.9541
2025-08-20 22:54:54 [INFO]: Epoch 003 - training loss (MAE): 0.8575
2025-08-20 22:54:55 [INFO]: Epoch 004 - training loss (MAE): 0.8102
2025-08-20 22:54:56 [INFO]: Epoch 005 - training loss (MAE): 0.7972
2025-08-20 22:54:56 [INFO]: Epoch 006 - training loss (MAE): 0.7641
2025-08-20 22:54:57 [INFO]: Epoch 007 - training loss (MAE): 0.7450
2025-08-20 22:54:57 [INFO]: Epoch 008 - training loss (MAE): 0.7076
2025-08-20 22:54:58 [INFO]: Epoch 009 - training loss (MAE): 0.7185
2025-08-20 22:54:59 [INFO]: Epoch 010 - training loss (MAE): 0.6880
2025-08-20 22:54:59 [INFO]: Epoch 011 - training loss (MAE): 0.7069
2025-08-20 22:55:00 [INFO]: Epoch 012 - training loss (MAE): 0.6803
2025-08-20 22:55:00 [INFO]: Epoch 013 - training loss (MAE): 0.6818
2025-08-20 22:55:01 [INFO]: Epoch 014 - training loss (MAE): 0.6717
2025-08-20 22:55:02 [INFO]: Epoch 015 - training loss (MAE): 0.6818
2025-08-20 22:55:02 [INFO]: Epoch 016 - training loss (MAE): 0.6689
2025-08-20 22:55:03 [INFO]: Epoch 017 - training loss (MAE): 0.6468
2025-08-20 22:55:04 [INFO]: Epoch 018 - training loss (MAE): 0.6377
2025-08-20 22:55:04 [INFO]: Epoch 019 - training loss (MAE): 0.6423
2025-08-20 22:55:05 [INFO]: Epoch 020 - training loss (MAE): 0.6370
2025-08-20 22:55:05 [INFO]: Epoch 021 - training loss (MAE): 0.6172
2025-08-20 22:55:06 [INFO]: Epoch 022 - training loss (MAE): 0.6244
2025-08-20 22:55:07 [INFO]: Epoch 023 - training loss (MAE): 0.6344
2025-08-20 22:55:07 [INFO]: Epoch 024 - training loss (MAE): 0.6175
2025-08-20 22:55:08 [INFO]: Epoch 025 - training loss (MAE): 0.5964
2025-08-20 22:55:08 [INFO]: Epoch 026 - training loss (MAE): 0.6245
2025-08-20 22:55:09 [INFO]: Epoch 027 - training loss (MAE): 0.6238
2025-08-20 22:55:10 [INFO]: Epoch 028 - training loss (MAE): 0.6102
2025-08-20 22:55:10 [INFO]: Epoch 029 - training loss (MAE): 0.6250
2025-08-20 22:55:11 [INFO]: Epoch 030 - training loss (MAE): 0.5926
2025-08-20 22:55:11 [INFO]: Epoch 031 - training loss (MAE): 0.6122
2025-08-20 22:55:12 [INFO]: Epoch 032 - training loss (MAE): 0.6137
2025-08-20 22:55:13 [INFO]: Epoch 033 - training loss (MAE): 0.6098
2025-08-20 22:55:13 [INFO]: Epoch 034 - training loss (MAE): 0.5943
2025-08-20 22:55:14 [INFO]: Epoch 035 - training loss (MAE): 0.5940
2025-08-20 22:55:15 [INFO]: Epoch 036 - training loss (MAE): 0.5878
2025-08-20 22:55:15 [INFO]: Epoch 037 - training loss (MAE): 0.6178
2025-08-20 22:55:16 [INFO]: Epoch 038 - training loss (MAE): 0.5990
2025-08-20 22:55:16 [INFO]: Epoch 039 - training loss (MAE): 0.5758
2025-08-20 22:55:17 [INFO]: Epoch 040 - training loss (MAE): 0.5843
2025-08-20 22:55:17 [INFO]: Finished training. The best model is from epoch#39.
   ⏱️ Epoch time: 24.57s, Memory Δ: +50.7MB
📊 Performance monitoring stopped

📊 SAITS Training Performance Summary:
   • Total Training Time: 49.4s
Enhanced Evaluation Phase...
📦 Small dataset (522 samples), using standard prediction
Imputation Metrics (Artificial Missing Values):
   • MAE: 0.1836
   • R²: 0.9273
   • RMSE: 0.2651
   • Evaluated Points: 1777
📊 Small dataset detected:
   - Total rows: 8352
   - Wells: 174
   - Avg rows/well: 48.0
   - Valid data ratio: 0.0%
   - Recommended sequence length: 16
   - Recommended step: 4
🔧 Adjusted parameters for small dataset:
   - Sequence length: 16 → 16
   - Step: 1 → 4
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: PREDICTION
  - Using feature columns only for valid interval detection
  - Target column will be included in sequences as NaN for model prediction
Creating sequences | Current: WELL_190: 100%|████████████| 174/174 wells [00:00<00:00, 282.11well/s]    

📊 Well Processing Summary:
   • Total wells processed: 174
   • Successful: 174
   • Failed: 0
   • Total valid intervals: 174
   • Total sequences created: 1,566
Enhanced sequences shape: (1566, 16, 6), type: <class 'numpy.ndarray'>, dtype: float64
🧠 Large dataset detected (1,566 samples), using memory optimization

==================================================
[MEM] MEMORY STATUS
==================================================
System Memory:
   * Total: 31.7 GB
   * Available: 12.2 GB
   * Usage: 61.4%

GPU Memory:
   * Total: 4.0 GB
   * Allocated: 0.1 GB
   * Reserved: 0.2 GB
   * Free: 3.9 GB
==================================================
🧠 Memory-optimized prediction for 1,566 samples
[MEM] Calculated optimal batch size: 32
   * Available memory: 3166.9 MB
   * Estimated usage: 30.5 MB
   • Using batch size: 32
SAITS Prediction: 100%|█████████████████████████████████████████████| 49/49 [00:16<00:00,  2.93batch/s]
✅ SAITS Prediction: 100% [██████████████████████████] Done
✅ Batch alignment verified: 1566 predictions for 1566 input samples

📊 Memory status after processing:

==================================================
[MEM] MEMORY STATUS
==================================================
System Memory:
   * Total: 31.7 GB
   * Available: 12.3 GB
   * Usage: 61.4%

GPU Memory:
   * Total: 4.0 GB
   * Allocated: 0.1 GB
   * Reserved: 0.2 GB
   * Free: 3.9 GB
==================================================
❌ Enhanced training failed: operands could not be broadcast together with shapes (8352,) (25056,)      
   Falling back to original function...
--- Running Deep Learning Model: SAITS (Self-Attention) ---
CuDNN benchmark enabled for performance.
Automatic well assignment:
  - Training/Validation wells: ['B-G-10', 'B-L-9', 'B-G-6', 'EB-1', 'B-L-1', 'B-L-6']
  - Test wells: ['B-L-2.G1', 'B-L-15']
   Using adaptive validation ratio: 30.0% (median well size: 7979)
   Minimum well size threshold: 12 (validation ratio: 30.0%)
Flexible Split Report:
  - Wells for Training/Validation: 6
  - Wells for Final Testing: 2
  - Train Samples (Shallow part of train wells): 32126
  - Validation Samples (Deeper part of train wells): 13772
  - Test Samples (Entirely separate wells): 11958

Running data leakage detection on splits...
🔍 Performing comprehensive data leakage check...
============================================================
🔍 Detecting perfect correlation leakage...
🚨 POTENTIAL DATA LEAKAGE DETECTED!
   TEST split:
     - TVDSS: correlation = 0.966
🕐 Validating temporal split integrity...
✅ No temporal leakage detected in splits
🎯 Detecting target leakage in features...
✅ No target leakage detected in features
============================================================
📊 COMPREHENSIVE LEAKAGE CHECK SUMMARY
============================================================
🚨 DATA LEAKAGE DETECTED!
   Failed checks: 1/3
   Data quality score: 0.67

📋 All Recommendations:
   ⚠️ Suspiciously high correlations detected (>0.95)
   • Check if target information is leaking into features
   • Verify temporal ordering of data splits
   • Review feature engineering process
   • Consider removing highly correlated features
   ✅ No temporal leakage detected in splits
   ✅ No target leakage detected in features
============================================================
⚠️ Data leakage detected but continuing with training...
   Review the recommendations above to improve data quality.

Using enhanced normalization with winsorization...
Normalized 'GR': method=standard, valid_data=31964/32126 (99.5%)
Normalized 'NPHI': method=standard, valid_data=24749/32126 (77.0%)
Normalized 'RHOB': method=standard, valid_data=20743/32126 (64.6%)
Normalized 'RT': method=standard, valid_data=31442/32126 (97.9%)
Normalized 'TVDSS': method=standard, valid_data=32126/32126 (100.0%)
Normalized 'P-WAVE': method=standard, valid_data=21526/32126 (67.0%)
📊 Small dataset detected:
   - Total rows: 32126
   - Wells: 6
   - Avg rows/well: 5354.3
   - Valid data ratio: 0.0%
   - Recommended sequence length: 16
   - Recommended step: 16
🔧 Adjusted parameters for small dataset:
   - Sequence length: 64 → 16
   - Step: 1 → 16
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: TRAINING
Creating sequences | Current: B-L-6: 100%|████████████████████| 6/6 wells [00:00<00:00, 19.45well/s]    

📊 Well Processing Summary:
   • Total wells processed: 6
   • Successful: 6
   • Failed: 0
   • Total valid intervals: 15
   • Total sequences created: 1,179
Enhanced sequences shape: (1179, 16, 6), type: <class 'numpy.ndarray'>, dtype: float64
Using enhanced normalization with winsorization...
📊 Small dataset detected:
   - Total rows: 13772
   - Wells: 6
   - Avg rows/well: 2295.3
   - Valid data ratio: 0.0%
   - Recommended sequence length: 16
   - Recommended step: 16
🔧 Adjusted parameters for small dataset:
   - Sequence length: 64 → 16
   - Step: 1 → 16
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: TRAINING
Creating sequences | Current: B-L-6: 100%|████████████████████| 6/6 wells [00:00<00:00, 56.28well/s]    

📊 Well Processing Summary:
   • Total wells processed: 6
   • Successful: 6
   • Failed: 0
   • Total valid intervals: 6
   • Total sequences created: 391
Enhanced sequences shape: (391, 16, 6), type: <class 'numpy.ndarray'>, dtype: float64
📚 Introducing missingness for imputation training.
Using enhanced missing value introduction with realistic patterns...
Introduced 20.0% missing values (22595 elements)
Pattern: 10186 random + 12409 chunked
Enhanced missing sequences shape: (1179, 16, 6), type: <class 'numpy.ndarray'>, dtype: float64
Using enhanced missing value introduction with realistic patterns...
Introduced 19.9% missing values (7460 elements)
Pattern: 3378 random + 4082 chunked
Enhanced missing sequences shape: (391, 16, 6), type: <class 'numpy.ndarray'>, dtype: float64
Converting sequences to tensors...
   Processing train_sequences_missing: current type is <class 'numpy.ndarray'>
   Changing train_sequences_missing dtype from float64 to float32.
   train_sequences_missing tensor created with shape: torch.Size([1179, 16, 6]), dtype: torch.float32   
   Processing train_sequences_true: current type is <class 'numpy.ndarray'>
   Changing train_sequences_true dtype from float64 to float32.
   train_sequences_true tensor created with shape: torch.Size([1179, 16, 6]), dtype: torch.float32      
   Processing val_sequences_missing: current type is <class 'numpy.ndarray'>
   Changing val_sequences_missing dtype from float64 to float32.
   val_sequences_missing tensor created with shape: torch.Size([391, 16, 6]), dtype: torch.float32      
   Processing val_sequences_true: current type is <class 'numpy.ndarray'>
   Changing val_sequences_true dtype from float64 to float32.
   val_sequences_true tensor created with shape: torch.Size([391, 16, 6]), dtype: torch.float32
   Tensor shapes - Train: torch.Size([1179, 16, 6]), Truth: torch.Size([1179, 16, 6])
   📏 Detected actual sequence length: 16
🔧 Applied fixed parameters for SAITS (Self-Attention): {}
🔍 Pre-checking PyPOTS availability for SAITS (Self-Attention) model...
🔄 TensorFlow cache reset - will re-check availability
🔧 TensorFlow environment configured for compatibility (TF 2.15.1 available)
✅ TensorFlow 2.15.1 loaded (CPU-only)
✅ PyPOTS imported successfully with TensorFlow backend
✅ PyPOTS availability confirmed for SAITS (Self-Attention) model
Initialized SAITSModel with:
   - Features: 6
   - Sequence length: 16
   - Epochs: 40
   - Batch size: 64
   - Learning rate: 0.002

🔍 Performing comprehensive SAITS environment validation...

📋 Step 1: Environment Validation
✅ PyPOTS imported successfully with TensorFlow backend
   TensorFlow: available
   PyPOTS: available
   GPU: not_available

💡 Recommendations:
   💻 CPU-only mode - consider GPU setup
   ✅ PyPOTS ready for SAITS model

📋 Step 2: TensorFlow GPU Configuration
💻 No GPUs detected - using CPU mode
💻 CPU mode configured - consider GPU setup for better performance

💡 Performance Optimization Tips:
   💻 CPU-only mode detected
   💡 Consider installing CUDA-compatible TensorFlow for GPU acceleration
   💡 Use smaller batch sizes to avoid memory issues
   💡 Consider using distributed training for large datasets
✅ TensorFlow 2.15.1 ready

📋 Step 3: PyPOTS Import and Configuration
✅ PyPOTS imported successfully with TensorFlow backend
✅ PyPOTS components successfully imported: pypots=0.19, SAITS=SAITS

🎯 SAITS Model Ready for Training!
   💻 CPU mode - training will be slower but functional
🚀 Selected GPU 0: NVIDIA T550 Laptop GPU
   Memory: 4.3 GB
   Compute Capability: 7.5
ℹ️ TF32 available but not enabled (pre-Ampere GPU)
🔥 SDPA optimized backends enabled (Flash Attention support)
⚡ Mixed precision training available
✅ GPU initialized successfully
🚀 SAITS using GPU with fallback protection: cuda
✅ SAITS (Self-Attention) model created successfully
🔧 Forcing model initialization to prepare optimizer for schedulers...
🔧 Initializing SAITS model...
2025-08-20 22:55:36 [INFO]: Using the given device: cuda
2025-08-20 22:55:36 [WARNING]: ‼️ saving_path not given. Model files and tensorboard file will not be saaved.
2025-08-20 22:55:36 [INFO]: Using customized MAE as the training loss function.
2025-08-20 22:55:36 [INFO]: Using customized MSE as the validation metric function.
2025-08-20 22:55:36 [INFO]: SAITS initialized with the given hyperparameters, the number of trainable parameters: 3,164,864
✅ SAITS model initialized successfully
   - Parameters: ~662,528
   - Memory usage: ~4.5 MB
Training phase...
   About to train model with:
   - train_tensor type: <class 'torch.Tensor'>, shape: torch.Size([1179, 16, 6]), dtype: torch.float32  
   - truth_tensor type: <class 'torch.Tensor'>, shape: torch.Size([1179, 16, 6]), dtype: torch.float32  
🔧 Initializing training stability utilities...
INFO:preprocessing.deep_model.stability_preprocessing:🔧 Initialized gradient clipper for saits with max_norm=1.0
   ℹ️ Skipping AdaptiveLRScheduler for PyPOTS model (saits); optimizer is managed internally.
   ✅ Gradient clipper (saits) initialized
Training PyPOTS-based model: SAITS (Self-Attention)
   Using tensor interface with batch_size=64, epochs=40
📊 Performance monitoring started
📊 Performance monitoring started for SAITS training
⚡ Mixed precision training enabled
🚀 Mixed precision training enabled via GPUManager (enhanced)
2025-08-20 22:55:37 [INFO]: Epoch 001 - training loss (MAE): 1.2240
2025-08-20 22:55:38 [INFO]: Epoch 002 - training loss (MAE): 0.7651
2025-08-20 22:55:38 [INFO]: Epoch 003 - training loss (MAE): 0.6842
2025-08-20 22:55:39 [INFO]: Epoch 004 - training loss (MAE): 0.6425
2025-08-20 22:55:39 [INFO]: Epoch 005 - training loss (MAE): 0.6173
2025-08-20 22:55:40 [INFO]: Epoch 006 - training loss (MAE): 0.5846
2025-08-20 22:55:41 [INFO]: Epoch 007 - training loss (MAE): 0.5563
2025-08-20 22:55:41 [INFO]: Epoch 008 - training loss (MAE): 0.5345
2025-08-20 22:55:42 [INFO]: Epoch 009 - training loss (MAE): 0.5159
2025-08-20 22:55:42 [INFO]: Epoch 010 - training loss (MAE): 0.5048
2025-08-20 22:55:43 [INFO]: Epoch 011 - training loss (MAE): 0.4926
2025-08-20 22:55:44 [INFO]: Epoch 012 - training loss (MAE): 0.4914
2025-08-20 22:55:44 [INFO]: Epoch 013 - training loss (MAE): 0.4799
2025-08-20 22:55:45 [INFO]: Epoch 014 - training loss (MAE): 0.4685
2025-08-20 22:55:45 [INFO]: Epoch 015 - training loss (MAE): 0.4679
2025-08-20 22:55:46 [INFO]: Epoch 016 - training loss (MAE): 0.4535
2025-08-20 22:55:47 [INFO]: Epoch 017 - training loss (MAE): 0.4474
2025-08-20 22:55:47 [INFO]: Epoch 018 - training loss (MAE): 0.4497
2025-08-20 22:55:48 [INFO]: Epoch 019 - training loss (MAE): 0.4498
2025-08-20 22:55:48 [INFO]: Epoch 020 - training loss (MAE): 0.4339
2025-08-20 22:55:49 [INFO]: Epoch 021 - training loss (MAE): 0.4368
2025-08-20 22:55:50 [INFO]: Epoch 022 - training loss (MAE): 0.4233
2025-08-20 22:55:50 [INFO]: Epoch 023 - training loss (MAE): 0.4234
2025-08-20 22:55:51 [INFO]: Epoch 024 - training loss (MAE): 0.4098
2025-08-20 22:55:51 [INFO]: Epoch 025 - training loss (MAE): 0.4111
2025-08-20 22:55:52 [INFO]: Epoch 026 - training loss (MAE): 0.4039
2025-08-20 22:55:53 [INFO]: Epoch 027 - training loss (MAE): 0.4096
2025-08-20 22:55:53 [INFO]: Epoch 028 - training loss (MAE): 0.4114
2025-08-20 22:55:54 [INFO]: Epoch 029 - training loss (MAE): 0.3959
2025-08-20 22:55:54 [INFO]: Epoch 030 - training loss (MAE): 0.3939
2025-08-20 22:55:55 [INFO]: Epoch 031 - training loss (MAE): 0.3946
2025-08-20 22:55:55 [INFO]: Epoch 032 - training loss (MAE): 0.3850
2025-08-20 22:55:56 [INFO]: Epoch 033 - training loss (MAE): 0.3839
2025-08-20 22:55:57 [INFO]: Epoch 034 - training loss (MAE): 0.3795
2025-08-20 22:55:57 [INFO]: Epoch 035 - training loss (MAE): 0.3732
2025-08-20 22:55:58 [INFO]: Epoch 036 - training loss (MAE): 0.3697
2025-08-20 22:55:58 [INFO]: Epoch 037 - training loss (MAE): 0.3699
2025-08-20 22:55:59 [INFO]: Epoch 038 - training loss (MAE): 0.3667
2025-08-20 22:56:00 [INFO]: Epoch 039 - training loss (MAE): 0.3634
2025-08-20 22:56:00 [INFO]: Epoch 040 - training loss (MAE): 0.3704
2025-08-20 22:56:00 [INFO]: Finished training. The best model is from epoch#39.
   ⏱️ Epoch time: 23.91s, Memory Δ: +50.7MB
📊 Performance monitoring stopped

📊 SAITS Training Performance Summary:
   • Total Training Time: 73.3s
Enhanced Evaluation Phase...
📦 Small dataset (391 samples), using standard prediction
Imputation Metrics (Artificial Missing Values):
   • MAE: 0.2165
   • R²: 0.6953
   • RMSE: 0.2897
   • Evaluated Points: 1264
📊 Small dataset detected:
   - Total rows: 13772
   - Wells: 6
   - Avg rows/well: 2295.3
   - Valid data ratio: 0.0%
   - Recommended sequence length: 16
   - Recommended step: 4
🔧 Adjusted parameters for small dataset:
   - Sequence length: 16 → 16
   - Step: 1 → 4
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: PREDICTION
  - Using feature columns only for valid interval detection
  - Target column will be included in sequences as NaN for model prediction
Creating sequences | Current: B-L-6: 100%|████████████████████| 6/6 wells [00:00<00:00, 15.13well/s]    

📊 Well Processing Summary:
   • Total wells processed: 6
   • Successful: 6
   • Failed: 0
   • Total valid intervals: 6
   • Total sequences created: 1,560
Enhanced sequences shape: (1560, 16, 6), type: <class 'numpy.ndarray'>, dtype: float64
🧠 Large dataset detected (1,560 samples), using memory optimization

==================================================
[MEM] MEMORY STATUS
==================================================
System Memory:
   * Total: 31.7 GB
   * Available: 12.2 GB
   * Usage: 61.5%

GPU Memory:
   * Total: 4.0 GB
   * Allocated: 0.2 GB
   * Reserved: 0.2 GB
   * Free: 3.8 GB
==================================================
🧠 Memory-optimized prediction for 1,560 samples
[MEM] Calculated optimal batch size: 32
   * Available memory: 3118.5 MB
   * Estimated usage: 30.5 MB
   • Using batch size: 32
SAITS Prediction: 100%|█████████████████████████████████████████████| 49/49 [00:16<00:00,  2.89batch/s] 
✅ SAITS Prediction: 100% [██████████████████████████] Done
✅ Batch alignment verified: 1560 predictions for 1560 input samples

📊 Memory status after processing:

==================================================
[MEM] MEMORY STATUS
==================================================
System Memory:
   * Total: 31.7 GB
   * Available: 12.2 GB
   * Usage: 61.5%

GPU Memory:
   * Total: 4.0 GB
   * Allocated: 0.2 GB
   * Reserved: 0.2 GB
   * Free: 3.8 GB
==================================================
❌ Fallback also failed: operands could not be broadcast together with shapes (6256,) (24960,)
[MEM] Memory cleared
saits failed with error: Both optimized and fallback implementations failed. Optimization error: operands could not be broadcast together with shapes (6256,) (24960,) , Fallback error: operands could not be broadcast together with shapes (6256,) (24960,)

Batch execution summary:
   • Successful models: 0
   • Failed models: 1
   • Failed: saits
All models failed. Continuing to next step...

Step 10: Configure output options
No successful models to process. Skipping to next step...